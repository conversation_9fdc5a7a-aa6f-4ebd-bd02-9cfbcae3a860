// hooks/useAuthProtection.ts
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { isAuthenticated } from '../lib/utils';

const useAuthProtection = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const publicPaths = ['/auth/signin', '/auth/signup'];

    if (!isAuthenticated() && !publicPaths.includes(location.pathname)) {
      navigate('/auth/signin');  // Redirect to sign-in if not authenticated
    }
  }, [location, navigate]);
};

export default useAuthProtection;
