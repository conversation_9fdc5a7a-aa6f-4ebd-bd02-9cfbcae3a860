import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService, SubscriptionPlan, SubscriptionUsage } from '../services/api';
import Breadcrumb from '../components/Breadcrumbs/Breadcrumb';

interface PlanChangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: SubscriptionPlan | null;
  onConfirm: (planKey: string) => void;
  loading: boolean;
}

const PlanChangeModal: React.FC<PlanChangeModalProps> = ({ 
  isOpen, 
  onClose, 
  plan, 
  onConfirm, 
  loading 
}) => {
  if (!isOpen || !plan) return null;

  const isUpgrade = plan.isUpgrade;
  const isDowngrade = plan.isDowngrade;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-4">
        <div className="p-6">
          <h3 className="text-xl font-bold text-neutral-gray-dark mb-4">
            {isUpgrade ? 'Upgrade' : isDowngrade ? 'Downgrade' : 'Change'} Subscription Plan
          </h3>
          
          <div className="mb-6">
            <div className="bg-background-light rounded-lg p-4 mb-4">
              <h4 className="font-semibold text-neutral-gray-dark mb-2">{plan.name}</h4>
              <div className="text-2xl font-bold text-primary-blue mb-2">
                ${plan.price}<span className="text-sm font-normal text-text-secondary">/month</span>
              </div>
              <div className="space-y-1 text-sm">
                <p className="text-text-secondary">• Up to {plan.maxUsers} users</p>
                <p className="text-text-secondary">• Up to {plan.maxDrivers} drivers</p>
              </div>
            </div>

            <div className="space-y-2">
              <h5 className="font-medium text-neutral-gray-dark">Features included:</h5>
              <ul className="space-y-1">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-text-secondary">
                    <svg className="w-4 h-4 text-accent-green mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {isDowngrade && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L3.316 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                  <p className="text-sm font-medium text-yellow-800">Downgrade Warning</p>
                  <p className="text-sm text-yellow-700 mt-1">
                    Please ensure your current usage is within the new plan limits before downgrading.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-border-color rounded-lg text-text-secondary hover:bg-background-light transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              onClick={() => onConfirm(plan.key)}
              disabled={loading}
              className={`flex-1 px-4 py-2 rounded-lg text-white font-medium transition-colors ${
                isUpgrade 
                  ? 'bg-accent-green hover:bg-accent-green/90' 
                  : isDowngrade 
                  ? 'bg-secondary-orange hover:bg-secondary-orange/90'
                  : 'bg-primary-blue hover:bg-primary-blue/90'
              } disabled:opacity-50`}
            >
              {loading ? 'Processing...' : `Confirm ${isUpgrade ? 'Upgrade' : isDowngrade ? 'Downgrade' : 'Change'}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const SubscriptionManagement: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const [subscriptionUsage, setSubscriptionUsage] = useState<SubscriptionUsage | null>(null);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [changeModalOpen, setChangeModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [changeLoading, setChangeLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (isAdmin()) {
      fetchSubscriptionData();
    } else {
      setError('Admin access required to view subscription information');
      setLoading(false);
    }
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      const [usageResponse, plansResponse] = await Promise.all([
        apiService.getSubscriptionUsage(),
        apiService.getSubscriptionPlans()
      ]);
      
      setSubscriptionUsage(usageResponse.data);
      setAvailablePlans(plansResponse.data);
    } catch (error: any) {
      console.error('Error fetching subscription data:', error);
      setError(error.response?.data?.message || 'Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handlePlanChange = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setChangeModalOpen(true);
  };

  const handleConfirmPlanChange = async (planKey: string) => {
    if (!selectedPlan) return;

    try {
      setChangeLoading(true);
      setError('');
      setSuccess('');

      if (selectedPlan.isUpgrade) {
        await apiService.upgradeSubscription(planKey);
        setSuccess('Subscription upgraded successfully!');
      } else if (selectedPlan.isDowngrade) {
        await apiService.downgradeSubscription(planKey);
        setSuccess('Subscription downgraded successfully!');
      }

      setChangeModalOpen(false);
      setSelectedPlan(null);
      fetchSubscriptionData();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to change subscription plan');
    } finally {
      setChangeLoading(false);
    }
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-secondary-orange';
    return 'text-accent-green';
  };

  const getUsageBarColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-secondary-orange';
    return 'bg-accent-green';
  };

  if (!isAdmin()) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500 rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-neutral-gray-dark mb-2">Access Denied</h2>
          <p className="text-neutral-gray">Admin access required to view subscription information</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-blue rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-neutral-gray-dark mb-2">Loading Subscription...</h2>
          <p className="text-neutral-gray">Please wait while we fetch subscription information</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Breadcrumb pageName="Subscription Management" />
      
      <div className="space-y-6">
        {/* Error/Success Messages */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <p className="text-green-600 text-sm">{success}</p>
            </div>
          </div>
        )}

        {/* Current Subscription & Usage */}
        {subscriptionUsage && (
          <div className="professional-table">
            <div className="p-6 border-b border-border-color">
              <h2 className="text-2xl font-bold text-text-primary mb-1">Current Subscription</h2>
              <p className="text-text-secondary">Manage your subscription plan and monitor usage</p>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Current Plan */}
                <div className="bg-background-light rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-neutral-gray-dark">Current Plan</h3>
                    <span className="px-3 py-1 bg-primary-blue text-white text-sm font-medium rounded-full">
                      Active
                    </span>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="text-xl font-bold text-primary-blue mb-1">{subscriptionUsage.plan.name}</h4>
                    <div className="text-2xl font-bold text-neutral-gray-dark">
                      ${subscriptionUsage.plan.price}
                      <span className="text-sm font-normal text-text-secondary">/month</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-text-secondary">Max Users:</span>
                      <span className="font-medium">{subscriptionUsage.plan.maxUsers}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-text-secondary">Max Drivers:</span>
                      <span className="font-medium">{subscriptionUsage.plan.maxDrivers}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-text-secondary">Billing Period:</span>
                      <span className="font-medium">
                        {new Date(subscriptionUsage.billingPeriod.start).toLocaleDateString()} - {new Date(subscriptionUsage.billingPeriod.end).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Usage Statistics */}
                <div className="bg-background-light rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-neutral-gray-dark mb-4">Usage Statistics</h3>
                  
                  <div className="space-y-4">
                    {/* Users Usage */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-text-secondary">Users</span>
                        <span className={`text-sm font-bold ${getUsageColor(subscriptionUsage.users.percentage)}`}>
                          {subscriptionUsage.users.current} / {subscriptionUsage.users.limit}
                        </span>
                      </div>
                      <div className="w-full bg-neutral-gray bg-opacity-20 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${getUsageBarColor(subscriptionUsage.users.percentage)}`}
                          style={{ width: `${Math.min(subscriptionUsage.users.percentage, 100)}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-text-secondary mt-1">
                        <span>0</span>
                        <span className={getUsageColor(subscriptionUsage.users.percentage)}>
                          {subscriptionUsage.users.percentage.toFixed(1)}%
                        </span>
                        <span>{subscriptionUsage.users.limit}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Available Plans */}
        <div className="professional-table">
          <div className="p-6 border-b border-border-color">
            <h2 className="text-2xl font-bold text-text-primary mb-1">Available Plans</h2>
            <p className="text-text-secondary">Choose the plan that best fits your business needs</p>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {availablePlans.map((plan) => (
                <div
                  key={plan.key}
                  className={`rounded-lg border-2 p-6 relative ${
                    plan.isCurrent 
                      ? 'border-primary-blue bg-primary-blue bg-opacity-5' 
                      : 'border-border-color bg-white hover:border-primary-blue hover:shadow-lg transition-all duration-200'
                  }`}
                >
                  {plan.isCurrent && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-primary-blue text-white px-3 py-1 rounded-full text-xs font-medium">
                        Current Plan
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-neutral-gray-dark mb-2">{plan.name}</h3>
                    <div className="text-3xl font-bold text-primary-blue">
                      ${plan.price}
                      <span className="text-sm font-normal text-text-secondary">/month</span>
                    </div>
                  </div>

                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between text-sm">
                      <span className="text-text-secondary">Users:</span>
                      <span className="font-medium">{plan.maxUsers}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-text-secondary">Drivers:</span>
                      <span className="font-medium">{plan.maxDrivers}</span>
                    </div>
                  </div>

                  <div className="space-y-2 mb-6">
                    <h4 className="font-medium text-neutral-gray-dark text-sm">Features:</h4>
                    <ul className="space-y-1">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-xs text-text-secondary">
                          <svg className="w-3 h-3 text-accent-green mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <button
                    onClick={() => handlePlanChange(plan)}
                    disabled={plan.isCurrent}
                    className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                      plan.isCurrent
                        ? 'bg-neutral-gray bg-opacity-20 text-text-secondary cursor-not-allowed'
                        : plan.isUpgrade
                        ? 'bg-accent-green text-white hover:bg-accent-green/90'
                        : plan.isDowngrade
                        ? 'bg-secondary-orange text-white hover:bg-secondary-orange/90'
                        : 'bg-primary-blue text-white hover:bg-primary-blue/90'
                    }`}
                  >
                    {plan.isCurrent 
                      ? 'Current Plan' 
                      : plan.isUpgrade 
                      ? 'Upgrade' 
                      : plan.isDowngrade 
                      ? 'Downgrade' 
                      : 'Select Plan'
                    }
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <PlanChangeModal
        isOpen={changeModalOpen}
        onClose={() => {
          setChangeModalOpen(false);
          setSelectedPlan(null);
        }}
        plan={selectedPlan}
        onConfirm={handleConfirmPlanChange}
        loading={changeLoading}
      />
    </>
  );
};

export default SubscriptionManagement;
