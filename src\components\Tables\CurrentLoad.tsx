import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import { AI_URL, BASE_URL } from '../../lib/constants';

// Define the Load type
interface Load {
  id: string;
  assistantId: string;
  phoneNumberId: string;
  type: string;
  createdAt: number;
  updatedAt: number;
  orgId: string;
  cost: number;
  assistant: {
    model: {
      model: string;
      messages: Array<{ role: string; content: string }>;
      provider: string;
      temperature: number;
    };
    firstMessage: string;
    firstMessageMode: string;
  };
  customer: {
    number: string;
    numberE164CheckEnabled: boolean;
  };
  status: string;
  phoneCallProvider: string;
  phoneCallProviderId: string;
  phoneCallTransport: string;
  recordingUrl: string;
  summary: string;
  monitor: {
    listenUrl: string;
    controlUrl: string;
  };
  userId: string;
}

// Component for displaying current load details
const CurrentLoad: React.FC = () => {
  const { id } = useParams<{ id: string }>(); // Get the load ID from the URL
  const [load, setLoad] = useState<Load | null>(null); // State for holding the load data
  const [error, setError] = useState<string | null>(null); // Error state
  const navigate = useNavigate(); // Hook for navigation

  useEffect(() => {
    const fetchCurrentLoad = async () => {
      try {
        const response = await axios.post(`${AI_URL}/get-call?call_id=${id}`);
        setLoad(response.data);
      } catch (error) {
        setError('Error fetching load data');
        console.error('Error:', error);
      }
    };

    fetchCurrentLoad();
  }, [id]);

  const handleBack = () => {
    navigate(-1); // Navigate to the previous page
  };

  if (error) {
    return <p>{error}</p>;
  }

  if (!load) {
    return <p>Loading...</p>;
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-md dark:bg-gray-800 dark:text-white">
      <h2 className="text-2xl font-semibold mb-4">Current Load Details</h2>

      <div className="mb-3">
        <span className="font-medium">Load ID:</span> {load.id}
      </div>

      <div className="mb-3">
        <span className="font-medium">Status:</span>{' '}
        <span
          className={`px-2 py-0.5 rounded-full text-xs font-medium ${load?.status === 'queued'
              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
              : load.status === 'completed'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
            }`}
        >
          {load?.status}
        </span>
      </div>

      <div className="mb-3">
        <span className="font-medium">Phone Number:</span> {load?.customer?.number}
      </div>

      <div className="mb-3">
        <span className="font-medium">Assistant Model:</span> {load?.assistant?.model?.model}
      </div>

      <div className="mb-3">
        <span className="font-medium">Assistant First Message:</span> {load?.assistant?.firstMessage}
      </div>

      <div className="mb-3">
        <span className="font-medium">Created At:</span> {new Date(load?.createdAt).toLocaleString()}
      </div>

      <div className="mb-3">
        <span className="font-medium">Updated At:</span> {new Date(load?.updatedAt).toLocaleString()}
      </div>

      <div className="mb-3">
        <span className="font-medium">Call Recording URL:</span> <a href={load?.recordingUrl} className="text-blue-600">{load?.recordingUrl}</a>
      </div>

      <div className="mb-3">
        <span className="font-medium">Call Summary:</span> {load?.summary}
      </div>

      {/* Back Button */}
      <button
        onClick={handleBack}
        className="mt-4 px-4 py-2 bg-black  text-white font-medium rounded-md "
      >
        Back
      </button>
    </div>
  );
};

export default CurrentLoad;
