import React, { ReactNode } from 'react';
import { useLocation } from 'react-router-dom';
import Header from '../components/Header/index';
import Sidebar from '../components/Sidebar/index';

const DefaultLayout: React.FC<{ children: ReactNode }> = ({ children }) => {
  const location = useLocation();

  // Conditionally hide the sidebar for specific routes
  const hideSidebar = location.pathname === '/auth/signin' || location.pathname === '/auth/signup';

  return (
    <div className="bg-background-light min-h-screen">
      {/* Professional Page Wrapper */}
      <div className="flex h-screen overflow-hidden">
        {/* Professional Sidebar - Always visible when not on auth pages */}
        {!hideSidebar && <Sidebar />}

        {/* Main Content Area */}
        <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
          {/* Professional Header */}
          {!hideSidebar && <Header />}

          {/* Main Content with Professional Spacing */}
          <main className="flex-1 bg-background-light">
            <div className="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-8">
              <div className="space-y-6">
                {children}
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default DefaultLayout;
