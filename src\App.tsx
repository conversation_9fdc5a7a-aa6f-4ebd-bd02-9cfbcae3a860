import { useEffect, useState } from 'react';
import { Route, Routes, useLocation, Navigate } from 'react-router-dom';

import Loader from './common/Loader';
import PageTitle from './components/PageTitle';
import DefaultLayout from './layout/DefaultLayout';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import SignIn from './pages/Authentication/SignIn';
import SignUp from './pages/Authentication/SignUp';
import ECommerce from './pages/Dashboard/ECommerce';
import Profile from './pages/Profile';
import Tables from './pages/Tables';
import TeamManagement from './pages/TeamManagement';
import TruckingCompanies from './pages/TruckingCompanies';
import DriverManagement from './pages/DriverManagement';
import LoadManagement from './pages/LoadManagement';
import CallLogs from './pages/CallLogs';
import SubscriptionManagement from './pages/SubscriptionManagement';
import CurrentLoad from './components/Tables/CurrentLoad';

// Main App component with authentication routing
function AppContent() {
  const [loading, setLoading] = useState<boolean>(true);
  const { pathname } = useLocation();
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  useEffect(() => {
    setTimeout(() => setLoading(false), 1000);
  }, []);

  if (loading || authLoading) {
    return <Loader />;
  }

  return (
    <Routes>
      {/* Public Routes */}
      <Route
        path="/auth/signin"
        element={
          isAuthenticated ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <>
              <PageTitle title="Sign In - XTrucker AI" />
              <SignIn />
            </>
          )
        }
      />
      <Route
        path="/auth/signup"
        element={
          isAuthenticated ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <>
              <PageTitle title="Sign Up - XTrucker AI" />
              <SignUp />
            </>
          )
        }
      />

      {/* Protected Routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Dashboard - XTrucker AI" />
              <ECommerce />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Profile - XTrucker AI" />
              <Profile />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/tables"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Tables - XTrucker AI" />
              <Tables />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/team"
        element={
          <ProtectedRoute requireAdmin={true}>
            <DefaultLayout>
              <PageTitle title="Team Management - XTrucker AI" />
              <TeamManagement />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/trucking-companies"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Trucking Companies - XTrucker AI" />
              <TruckingCompanies />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/drivers"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Driver Management - XTrucker AI" />
              <DriverManagement />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/loads"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Load Management - XTrucker AI" />
              <LoadManagement />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/call-logs"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Call Logs - XTrucker AI" />
              <CallLogs />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/subscription"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Subscription Management - XTrucker AI" />
              <SubscriptionManagement />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/call-log/:id"
        element={
          <ProtectedRoute>
            <DefaultLayout>
              <PageTitle title="Call Log - XTrucker AI" />
              <CurrentLoad />
            </DefaultLayout>
          </ProtectedRoute>
        }
      />

      {/* Default redirects */}
      <Route
        path="/"
        element={
          isAuthenticated ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <Navigate to="/auth/signin" replace />
          )
        }
      />
      <Route
        path="*"
        element={
          <Navigate to={isAuthenticated ? "/dashboard" : "/auth/signin"} replace />
        }
      />
    </Routes>
  );
}

// Main App wrapper with AuthProvider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
