import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService, TruckingCompany } from '../services/api';
import Breadcrumb from '../components/Breadcrumbs/Breadcrumb';

interface CompanyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Partial<TruckingCompany>) => void;
  loading: boolean;
  company?: TruckingCompany | null;
}

const CompanyModal: React.FC<CompanyModalProps> = ({ isOpen, onClose, onSave, loading, company }) => {
  const [formData, setFormData] = useState({
    name: '',
    mcNumber: '',
    usdotNumber: '',
    address: '',
    contactInfo: {
      email: '',
      phone: ''
    },
    insurance: {
      provider: '',
      expirationDate: ''
    },
    notes: ''
  });

  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name || '',
        mcNumber: company.mcNumber || '',
        usdotNumber: company.usdotNumber || '',
        address: company.address || '',
        contactInfo: {
          email: company.contactInfo?.email || '',
          phone: company.contactInfo?.phone || ''
        },
        insurance: {
          provider: company.insurance?.provider || '',
          expirationDate: company.insurance?.expirationDate || ''
        },
        notes: company.notes || ''
      });
    } else {
      setFormData({
        name: '',
        mcNumber: '',
        usdotNumber: '',
        address: '',
        contactInfo: {
          email: '',
          phone: ''
        },
        insurance: {
          provider: '',
          expirationDate: ''
        },
        notes: ''
      });
    }
  }, [company, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      if (parent === 'contactInfo') {
        setFormData({
          ...formData,
          contactInfo: {
            ...formData.contactInfo,
            [child]: value
          }
        });
      } else if (parent === 'insurance') {
        setFormData({
          ...formData,
          insurance: {
            ...formData.insurance,
            [child]: value
          }
        });
      }
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-neutral-gray-dark">
            {company ? 'Edit Trucking Company' : 'Add New Trucking Company'}
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-gray hover:text-neutral-gray-dark"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-lg font-semibold text-neutral-gray-dark mb-4">Basic Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Company Name *
                </label>
                <input
                  id="name"
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter company name"
                  required
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="mcNumber" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  MC Number *
                </label>
                <input
                  id="mcNumber"
                  type="text"
                  name="mcNumber"
                  value={formData.mcNumber}
                  onChange={handleChange}
                  placeholder="Enter MC number"
                  required
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="usdotNumber" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  USDOT Number *
                </label>
                <input
                  id="usdotNumber"
                  type="text"
                  name="usdotNumber"
                  value={formData.usdotNumber}
                  onChange={handleChange}
                  placeholder="Enter USDOT number"
                  required
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="contactInfo.phone" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Phone Number
                </label>
                <input
                  id="contactInfo.phone"
                  type="tel"
                  name="contactInfo.phone"
                  value={formData.contactInfo.phone}
                  onChange={handleChange}
                  placeholder="Enter phone number"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="contactInfo.email" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Email Address
                </label>
                <input
                  id="contactInfo.email"
                  type="email"
                  name="contactInfo.email"
                  value={formData.contactInfo.email}
                  onChange={handleChange}
                  placeholder="Enter email address"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>


            </div>

            <div className="mt-4">
              <label htmlFor="address" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                Address
              </label>
              <input
                id="address"
                type="text"
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder="Enter company address"
                className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>
          </div>

          {/* Insurance Information */}
          <div>
            <h4 className="text-lg font-semibold text-neutral-gray-dark mb-4">Insurance Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="insurance.provider" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Insurance Provider
                </label>
                <input
                  id="insurance.provider"
                  type="text"
                  name="insurance.provider"
                  value={formData.insurance.provider}
                  onChange={handleChange}
                  placeholder="Enter insurance provider"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="insurance.expirationDate" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Insurance Expiration Date
                </label>
                <input
                  id="insurance.expirationDate"
                  type="date"
                  name="insurance.expirationDate"
                  value={formData.insurance.expirationDate}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-neutral-gray-dark mb-2">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              rows={3}
              className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-border-color rounded-lg text-neutral-gray-dark hover:bg-neutral-gray-light transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 truck-btn-primary flex justify-center items-center"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                company ? 'Update Company' : 'Add Company'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const TruckingCompanies: React.FC = () => {
  const { isAdmin } = useAuth();
  const [companies, setCompanies] = useState<TruckingCompany[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [editingCompany, setEditingCompany] = useState<TruckingCompany | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [verificationFilter, setVerificationFilter] = useState('');

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (verificationFilter) params.verified = verificationFilter === 'verified';

      const response = await apiService.getTruckingCompanies(params);
      setCompanies(response.data.companies || []);
    } catch (error) {
      console.error('Error fetching trucking companies:', error);
      setError('Failed to load trucking companies');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (data: Partial<TruckingCompany>) => {
    try {
      setModalLoading(true);
      if (editingCompany) {
        await apiService.updateTruckingCompany(editingCompany._id, data);
      } else {
        await apiService.createTruckingCompany(data as Omit<TruckingCompany, '_id' | 'createdAt'>);
      }
      setModalOpen(false);
      setEditingCompany(null);
      fetchCompanies();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to save trucking company');
    } finally {
      setModalLoading(false);
    }
  };

  const handleEdit = (company: TruckingCompany) => {
    setEditingCompany(company);
    setModalOpen(true);
  };

  const handleDelete = async (companyId: string) => {
    if (!window.confirm('Are you sure you want to delete this trucking company? This action cannot be undone.')) return;

    try {
      await apiService.deleteTruckingCompany(companyId);
      fetchCompanies();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to delete trucking company');
    }
  };







  const getVerificationBadge = (verificationStatus: { mcVerified: boolean; dotVerified: boolean; insuranceVerified: boolean }) => {
    const baseClasses = "px-3 py-1 rounded-full text-xs font-medium";
    const allVerified = verificationStatus.mcVerified && verificationStatus.dotVerified && verificationStatus.insuranceVerified;
    const someVerified = verificationStatus.mcVerified || verificationStatus.dotVerified || verificationStatus.insuranceVerified;

    if (allVerified) {
      return `${baseClasses} bg-accent-green bg-opacity-10 text-accent-green`;
    } else if (someVerified) {
      return `${baseClasses} bg-yellow-500 bg-opacity-10 text-yellow-600`;
    }
    return `${baseClasses} bg-neutral-gray bg-opacity-10 text-neutral-gray`;
  };

  const getVerificationText = (verificationStatus: { mcVerified: boolean; dotVerified: boolean; insuranceVerified: boolean }) => {
    const allVerified = verificationStatus.mcVerified && verificationStatus.dotVerified && verificationStatus.insuranceVerified;
    const someVerified = verificationStatus.mcVerified || verificationStatus.dotVerified || verificationStatus.insuranceVerified;

    if (allVerified) {
      return 'Fully Verified';
    } else if (someVerified) {
      return 'Partially Verified';
    }
    return 'Unverified';
  };

  const filteredCompanies = companies.filter(company => {
    const matchesSearch = !searchTerm ||
      company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.mcNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      company.usdotNumber.toLowerCase().includes(searchTerm.toLowerCase());

    // Since the new schema doesn't have a status field, we'll assume all companies are active
    // You may need to adjust this based on your actual backend implementation
    const matchesStatus = true; // Remove status filtering for now

    const allVerified = company.verificationStatus?.mcVerified && company.verificationStatus?.dotVerified && company.verificationStatus?.insuranceVerified;
    const matchesVerification = !verificationFilter ||
      (verificationFilter === 'verified' && allVerified) ||
      (verificationFilter === 'unverified' && !allVerified);

    return matchesSearch && matchesStatus && matchesVerification;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-blue rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-neutral-gray-dark mb-2">Loading Companies...</h2>
          <p className="text-neutral-gray">Please wait while we fetch trucking companies</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Breadcrumb pageName="Trucking Companies" />

      <div className="professional-table">
        {/* Header */}
        <div className="p-6 border-b border-border-color">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-1">Trucking Companies</h2>
              <p className="text-text-secondary">Manage trucking companies, verification status, and company details</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-text-secondary">
                Total: <span className="font-semibold text-text-primary">{filteredCompanies.length}</span> companies
              </div>
              {isAdmin() && (
                <button
                  onClick={() => {
                    setEditingCompany(null);
                    setModalOpen(true);
                  }}
                  className="truck-btn-primary flex items-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Company
                </button>
              )}
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search companies, MC, or USDOT numbers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>

            <select
              value={verificationFilter}
              onChange={(e) => setVerificationFilter(e.target.value)}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="">All Verification</option>
              <option value="verified">Verified</option>
              <option value="unverified">Unverified</option>
            </select>
          </div>
        </div>

        {error && (
          <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          {/* Table Header */}
          <div className="professional-table-header">
            <div className="grid grid-cols-10 gap-4 px-6 py-4 text-sm font-semibold text-text-primary">
              <div className="col-span-3">Company</div>
              <div className="col-span-2">MC/USDOT</div>
              <div className="col-span-2">Details</div>
              <div className="col-span-1">Verification</div>
              <div className="col-span-2">Actions</div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border-color">
            {filteredCompanies.length > 0 ? (
              filteredCompanies.map((company) => (
                <div
                  key={company._id}
                  className="professional-table-row grid grid-cols-10 gap-4 px-6 py-4 items-center"
                >
                  {/* Company */}
                  <div className="col-span-3">
                    <div>
                      <p className="font-medium text-text-primary">{company.name}</p>
                      <p className="text-sm text-text-secondary">{company.contactInfo?.phone}</p>
                      {company.contactInfo?.email && (
                        <p className="text-xs text-text-secondary">Email: {company.contactInfo.email}</p>
                      )}
                    </div>
                  </div>

                  {/* MC/USDOT */}
                  <div className="col-span-2">
                    <div>
                      <p className="text-sm font-medium text-text-primary">MC: {company.mcNumber}</p>
                      <p className="text-sm text-text-secondary">USDOT: {company.usdotNumber}</p>
                    </div>
                  </div>

                  {/* Status */}
                  <div className="col-span-2">
                    <div className="space-y-1">
                      <div className="text-sm text-text-secondary">Drivers: {company.numberOfDrivers}</div>
                      {company.insurance?.provider && (
                        <div className="text-xs text-text-secondary">Insurance: {company.insurance.provider}</div>
                      )}
                    </div>
                  </div>

                  {/* Verified */}
                  <div className="col-span-1">
                    <span className={getVerificationBadge(company.verificationStatus)}>
                      {getVerificationText(company.verificationStatus)}
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="col-span-2">
                    <div className="flex items-center gap-2">
                      {isAdmin() && (
                        <>
                          <button
                            onClick={() => handleEdit(company)}
                            className="p-2 text-primary-blue hover:bg-primary-blue hover:bg-opacity-10 rounded-lg transition-colors"
                            title="Edit company"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>



                          <button
                            onClick={() => handleDelete(company._id)}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                            title="Delete company"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-12 text-center">
                <div className="flex flex-col items-center gap-3">
                  <svg className="w-12 h-12 text-neutral-gray-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary mb-1">No trucking companies found</h3>
                    <p className="text-text-secondary">Start by adding trucking companies to manage your network</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <CompanyModal
        isOpen={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setEditingCompany(null);
        }}
        onSave={handleSave}
        loading={modalLoading}
        company={editingCompany}
      />
    </>
  );
};

export default TruckingCompanies;