//@ts-ignore
import React, { useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import axios from 'axios'; // For making API requests
import Logo from '../../images/logo/logo.png';
import { BASE_URL } from '../../lib/constants';
import { login } from '../../store/features/authSlice';
import { useDispatch } from 'react-redux';

const SignIn: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch()
  const params = window.location.href.split('?')[1]; // Get the part after the '?'
  const searchParams = new URLSearchParams(params); // Create URLSearchParams object with the extracted query string

  const extentsion = searchParams.get('extension')
  const EXTENSION_ID = searchParams.get('extension_id')

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await axios.post(`${BASE_URL}/auth/signin`, { email, password });

      if (response.data.success) {
        // Store token in localStorage
        localStorage.setItem('authToken', response.data.token);
        dispatch(login(response.data.user));


        if (extentsion) {
          // Send the credentials to the Chrome extension
          if (typeof chrome !== "undefined" && chrome.runtime) {
            console.log("Extension ID: ", EXTENSION_ID)
            chrome.runtime.sendMessage(
              EXTENSION_ID,
              { data: response.data.user, type: 'login-request' }, // Fix: 'login' should be a string
              (response) => {
                if (!response?.success) {
                  console.log("error sending message", response);
                  return response;
                }
                alert("signed in on extension close this window now")
                console.log("Success ::: ", response.message);
              }
            );

          } else {
            console.log('chrome.runtime is not available');
          }
        } else {
          // Redirect to the protected route (e.g., dashboard)
          navigate('/');
        }

      }
    } catch (err: any) {
      console.log(err)
      setError(err.response?.data?.message || 'Invalid credentials');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div className="flex flex-wrap items-center">
          <div className="hidden w-full xl:block xl:w-1/2">
            <div className="py-17.5 px-26 text-center">
              <Link className="mb-5.5 inline-block" to="/">
                <img className="block" src={Logo} alt="Logo" />
              </Link>
              <p className="2xl:px-20">
                Xtrucker AI has revolutionized our dispatch process. With AI-driven load matching and automated calls, we’ve seen a significant increase in efficiency and customer satisfaction.
              </p>
            </div>
          </div>

          <div className="w-full border-stroke dark:border-strokedark xl:w-1/2 xl:border-l-2">
            <div className="w-full p-4 sm:p-12.5 xl:p-17.5">
              <h2 className="mb-9 text-2xl font-bold text-black dark:text-white sm:text-title-xl2">
                Sign In to XTrucker AI
              </h2>

              {error && <div className="text-red-500 mb-4">{error}</div>}

              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label className="mb-2.5 block font-medium text-black dark:text-white">
                    Email
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      className="w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 pr-10 text-black outline-none focus:border-primary dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                    />
                  </div>
                </div>

                <div className="mb-6">
                  <label className="mb-2.5 block font-medium text-black dark:text-white">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="6+ Characters, 1 Capital letter"
                      className="w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 pr-10 text-black outline-none focus:border-primary dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                    />
                  </div>
                </div>

                <div className="mb-5">
                  <input
                    type="submit"
                    value={loading ? 'Signing in...' : 'Sign In'}
                    className="w-full cursor-pointer rounded-lg border border-primary bg-primary p-4 text-white transition hover:bg-opacity-90"
                    disabled={loading}
                  />
                </div>

                <div className="mt-6 text-center">
                  <p>
                    Don’t have an account?{' '}
                    <Link to="/auth/signup" className="text-primary">
                      Sign Up
                    </Link>
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SignIn;
