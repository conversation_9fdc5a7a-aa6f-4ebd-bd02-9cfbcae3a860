{"name": "Xtrcuker AI Admin", "private": true, "version": "1.3.8", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "author": {"name": "Xtrcuker AI"}, "dependencies": {"@reduxjs/toolkit": "^2.2.8", "apexcharts": "^3.41.0", "axios": "^1.7.7", "dotenv": "^16.4.7", "flatpickr": "^4.6.13", "headlessui": "^0.0.0", "jsvectormap": "^1.5.3", "match-sorter": "^6.3.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "react-redux": "^9.1.2", "react-router-dom": "^6.14.2", "react-toastify": "^9.1.3", "sort-by": "^0.0.2"}, "devDependencies": {"@types/chrome": "^0.0.277", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "file-loader": "^6.2.0", "postcss": "^8.4.27", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3.4.1", "vite": "^4.4.7", "webpack": "^5.88.2"}}