import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const SignUp: React.FC = () => {
  const [formData, setFormData] = useState({
    companyName: '',
    adminName: '',
    adminEmail: '',
    adminPhone: '',
    password: '',
    confirmPassword: '',
    subscriptionPlan: 'basic',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { signup } = useAuth();

  // Handle input field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    const { companyName, adminName, adminEmail, adminPhone, password, confirmPassword, subscriptionPlan } = formData;

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      setLoading(false);
      return;
    }

    try {
      // Call signup with the correct field names that match backend expectations
      await signup({
        companyName,
        adminName,
        adminEmail,
        adminPhone,
        password,
        subscriptionPlan
      });
      navigate('/dashboard');
    } catch (error: any) {
      setError(error.response?.data?.message || 'An error occurred during sign up');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-gray-light">
      <div className="max-w-6xl w-full mx-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="flex flex-wrap items-center">
            {/* Left Side - Branding */}
            <div className="hidden lg:block lg:w-1/2 bg-primary-blue">
              <div className="p-12 text-center text-white">
                <div className="mb-8">
                  <div className="w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                    </svg>
                  </div>
                  <h1 className="text-3xl font-bold">XTrucker AI</h1>
                  <p className="text-primary-blue-light mt-2">Professional Truck Dispatching</p>
                </div>
                <div className="text-left bg-white bg-opacity-10 rounded-lg p-6">
                  <h3 className="text-xl font-semibold mb-4">Start Your Journey</h3>
                  <ul className="space-y-3 text-left">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      AI-powered load matching
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Automated dispatcher calls
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Real-time fleet tracking
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Professional dashboard
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Right Side - Signup Form */}
            <div className="w-full lg:w-1/2">
              <div className="p-8 lg:p-12">
                <div className="mb-8">
                  <h2 className="text-3xl font-bold text-neutral-gray-dark mb-2">
                    Create Your Account
                  </h2>
                  <p className="text-neutral-gray">Start your professional dispatching journey</p>
                </div>

                {error && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-red-600 text-sm">{error}</p>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="companyName" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Company Name
                    </label>
                    <input
                      id="companyName"
                      type="text"
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleChange}
                      placeholder="Enter your company name"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div>
                    <label htmlFor="adminName" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Admin Full Name
                    </label>
                    <input
                      id="adminName"
                      type="text"
                      name="adminName"
                      value={formData.adminName}
                      onChange={handleChange}
                      placeholder="Enter admin full name"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div>
                    <label htmlFor="adminEmail" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Admin Email Address
                    </label>
                    <input
                      id="adminEmail"
                      type="email"
                      name="adminEmail"
                      value={formData.adminEmail}
                      onChange={handleChange}
                      placeholder="Enter admin email"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div>
                    <label htmlFor="adminPhone" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Admin Phone Number
                    </label>
                    <input
                      id="adminPhone"
                      type="tel"
                      name="adminPhone"
                      value={formData.adminPhone}
                      onChange={handleChange}
                      placeholder="Enter admin phone number"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div>
                    <label htmlFor="subscriptionPlan" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Subscription Plan
                    </label>
                    <select
                      id="subscriptionPlan"
                      name="subscriptionPlan"
                      value={formData.subscriptionPlan}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    >
                      <option value="basic">Basic Plan - Free (Up to 5 users)</option>
                      <option value="pro">Pro Plan - $99/month (Up to 25 users)</option>
                      <option value="enterprise">Enterprise Plan - $299/month (Unlimited users)</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Password
                    </label>
                    <input
                      id="password"
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder="Enter your password (min. 6 characters)"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Confirm Password
                    </label>
                    <input
                      id="confirmPassword"
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      placeholder="Re-enter your password"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      id="terms"
                      type="checkbox"
                      required
                      className="h-4 w-4 text-primary-blue focus:ring-primary-blue border-border-color rounded"
                    />
                    <label htmlFor="terms" className="ml-2 block text-sm text-neutral-gray">
                      I agree to the{' '}
                      <Link to="/terms" className="text-primary-blue hover:text-primary-blue-dark">
                        Terms of Service
                      </Link>{' '}
                      and{' '}
                      <Link to="/privacy" className="text-primary-blue hover:text-primary-blue-dark">
                        Privacy Policy
                      </Link>
                    </label>
                  </div>

                  <button
                    type="submit"
                    disabled={loading}
                    className="truck-btn-primary w-full flex justify-center items-center"
                  >
                    {loading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating account...
                      </>
                    ) : (
                      'Create Account'
                    )}
                  </button>
                </form>

                <div className="mt-8 text-center">
                  <p className="text-sm text-neutral-gray">
                    Already have an account?{' '}
                    <Link to="/auth/signin" className="font-medium text-primary-blue hover:text-primary-blue-dark">
                      Sign in
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
