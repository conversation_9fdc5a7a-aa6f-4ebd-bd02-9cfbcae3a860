import axios from 'axios';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { BASE_URL } from '../../lib/constants';



// Define types for Call Log and User
interface CallLog {
  cost: number;
  type: string;
  status: string;
  customer: any;
  _id: string;
  createdAt: string;
  duration: number;
  callerName: string;
  id: string; // Call Id
}

interface User {
  id: string;
  email: string;
  name: string;
  profile_image: string;
}

const TableOne = () => {

  const [callLogs, setCallLogs] = useState<CallLog[]>([]); // Type for call logs
  let navigate = useNavigate();

  useEffect(() => {
    // Retrieve user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      console.error('User not found in localStorage');
      return;
    }

    // Parse the user object
    const user: User = JSON.parse(storedUser);
    const userId = user?.id;

    if (!userId) {
      console.error('User ID not found');
      return;
    }

    // Fetch call logs by user ID
    const fetchCallLogs = async () => {
      try {
        const response = await axios.get<CallLog[]>(`${BASE_URL}/loads/call-logs/user/${userId}`);
        setCallLogs(response.data);
      } catch (error) {
        console.error('Error fetching call logs:', error);
      }
    };

    fetchCallLogs();
  }, []);

  // Handle row click to navigate to detailed call log page
  const handleRowClick = (callLogId: string) => {
    navigate(`/call-log/${callLogId}`); // Navigate to detailed call log page
  };


  const getCallTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'inbound':
        return (
          <svg className="w-4 h-4 text-accent-green" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
          </svg>
        );
      case 'outbound':
        return (
          <svg className="w-4 h-4 text-primary-blue" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-neutral-gray" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const getStatusBadge = (status: string) => {
    const statusLower = status?.toLowerCase();
    if (statusLower === 'completed') {
      return <span className="truck-status-active">Completed</span>;
    } else if (statusLower === 'pending') {
      return <span className="truck-status-pending">Pending</span>;
    } else {
      return <span className="truck-status-inactive">Unknown</span>;
    }
  };

  const formatDuration = (duration: number) => {
    if (!duration) return 'N/A';
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="professional-table">
      {/* Header */}
      <div className="p-6 border-b border-border-color">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-text-primary mb-1">Call Logs</h2>
            <p className="text-text-secondary">Track all customer communications and call activities</p>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-sm text-text-secondary">
              Total: <span className="font-semibold text-text-primary">{callLogs.length}</span> calls
            </span>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        {/* Table Header */}
        <div className="professional-table-header">
          <div className="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-text-primary">
            <div className="col-span-3">Date & Time</div>
            <div className="col-span-3">Customer</div>
            <div className="col-span-2">Type</div>
            <div className="col-span-2">Duration</div>
            <div className="col-span-2">Status</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="divide-y divide-border-color">
          {callLogs.length > 0 ? (
            callLogs.map((callLog) => (
              <div
                key={callLog.id}
                className="professional-table-row grid grid-cols-12 gap-4 px-6 py-4 items-center"
                onClick={() => handleRowClick(callLog.id)}
              >
                {/* Date & Time */}
                <div className="col-span-3">
                  <div className="flex flex-col">
                    <span className="font-medium text-text-primary">
                      {new Date(callLog?.createdAt).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </span>
                    <span className="text-sm text-text-secondary">
                      {new Date(callLog?.createdAt).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>

                {/* Customer */}
                <div className="col-span-3">
                  <div className="flex flex-col">
                    <span className="font-medium text-text-primary">
                      {callLog?.callerName || 'Unknown Caller'}
                    </span>
                    <span className="text-sm text-text-secondary">
                      {callLog?.customer?.number || 'No number'}
                    </span>
                  </div>
                </div>

                {/* Type */}
                <div className="col-span-2">
                  <div className="flex items-center gap-2">
                    {getCallTypeIcon(callLog?.type)}
                    <span className="font-medium text-text-primary capitalize">
                      {callLog?.type || 'Unknown'}
                    </span>
                  </div>
                </div>

                {/* Duration */}
                <div className="col-span-2">
                  <span className="font-medium text-text-primary">
                    {formatDuration(callLog?.duration)}
                  </span>
                </div>

                {/* Status */}
                <div className="col-span-2">
                  {getStatusBadge(callLog?.status)}
                </div>
              </div>
            ))
          ) : (
            <div className="px-6 py-12 text-center">
              <div className="flex flex-col items-center gap-3">
                <svg className="w-12 h-12 text-neutral-gray-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <div>
                  <h3 className="text-lg font-semibold text-text-primary mb-1">No call logs found</h3>
                  <p className="text-text-secondary">Call logs will appear here once you start making calls</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TableOne;
