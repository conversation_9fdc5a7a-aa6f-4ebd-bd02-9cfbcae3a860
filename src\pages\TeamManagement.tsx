import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService, DispatcherUser } from '../services/api';
import Breadcrumb from '../components/Breadcrumbs/Breadcrumb';
import {
  PageLoading,
  ErrorState,
  EmptyState,
  LoadingButton,
  Alert,
  SkeletonTable
} from '../components/LoadingStates';
import { useAsyncState, useFormState } from '../hooks/useAsyncState';

interface InviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInvite: (data: { name: string; email: string; role: 'admin' | 'agent' }) => void;
  loading: boolean;
}

const InviteModal: React.FC<InviteModalProps> = ({ isOpen, onClose, onInvite, loading }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'agent' as 'admin' | 'agent'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onInvite(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-neutral-gray-dark">Invite Team Member</h3>
          <button
            onClick={onClose}
            className="text-neutral-gray hover:text-neutral-gray-dark"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-neutral-gray-dark mb-2">
              Full Name
            </label>
            <input
              id="name"
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter full name"
              required
              className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-neutral-gray-dark mb-2">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email address"
              required
              className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            />
          </div>

          <div>
            <label htmlFor="role" className="block text-sm font-medium text-neutral-gray-dark mb-2">
              Role
            </label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="agent">Agent</option>
              <option value="admin">Admin</option>
            </select>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-border-color rounded-lg text-neutral-gray-dark hover:bg-neutral-gray-light transition-colors"
            >
              Cancel
            </button>
            <LoadingButton
              type="submit"
              loading={loading}
              className="flex-1"
            >
              Send Invitation
            </LoadingButton>
          </div>
        </form>
      </div>
    </div>
  );
};

const TeamManagement: React.FC = () => {
  const { user, company, isAdmin, canAddUsers } = useAuth();
  const [inviteModalOpen, setInviteModalOpen] = useState(false);

  // Use async state hook for team members
  const {
    data: teamMembers,
    loading,
    error,
    execute: fetchTeamMembers,
    retry: retryFetch,
    setError
  } = useAsyncState<DispatcherUser[]>(undefined, { initialData: [] });

  // Use form state hook for invite functionality
  const {
    loading: inviteLoading,
    error: inviteError,
    success: inviteSuccess,
    submit: submitInvite,
    reset: resetInviteState
  } = useFormState({
    onSuccess: () => {
      setInviteModalOpen(false);
      loadTeamMembers();
    }
  });

  useEffect(() => {
    loadTeamMembers();
  }, []);

  const loadTeamMembers = async () => {
    await fetchTeamMembers(() => apiService.getTeamMembers().then(response => response.data));
  };

  const handleInvite = async (data: { name: string; email: string; role: 'admin' | 'agent' }) => {
    await submitInvite(() => apiService.inviteTeamMember(data));
  };

  const handleRemoveMember = async (userId: string) => {
    if (!window.confirm('Are you sure you want to remove this team member?')) return;

    try {
      await apiService.removeTeamMember(userId);
      loadTeamMembers(); // Refresh the list
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to remove team member');
    }
  };

  const getRoleBadge = (role: string) => {
    const baseClasses = "px-3 py-1 rounded-full text-xs font-medium";
    if (role === 'admin') {
      return `${baseClasses} bg-secondary-orange bg-opacity-10 text-secondary-orange`;
    }
    return `${baseClasses} bg-accent-green bg-opacity-10 text-accent-green`;
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-3 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-accent-green bg-opacity-10 text-accent-green`;
      case 'pending':
        return `${baseClasses} bg-yellow-500 bg-opacity-10 text-yellow-600`;
      case 'inactive':
        return `${baseClasses} bg-red-500 bg-opacity-10 text-red-600`;
      default:
        return `${baseClasses} bg-neutral-gray bg-opacity-10 text-neutral-gray`;
    }
  };

  if (loading) {
    return (
      <>
        <Breadcrumb pageName="Team Management" />
        <PageLoading
          title="Loading Team..."
          subtitle="Please wait while we fetch your team members"
          icon={
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          }
        />
      </>
    );
  }

  if (error) {
    return (
      <>
        <Breadcrumb pageName="Team Management" />
        <ErrorState
          title="Failed to Load Team"
          message={error}
          onRetry={retryFetch}
          icon={
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          }
        />
      </>
    );
  }

  return (
    <>
      <Breadcrumb pageName="Team Management" />
      
      <div className="professional-table">
        {/* Header */}
        <div className="p-6 border-b border-border-color">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-1">Team Management</h2>
              <p className="text-text-secondary">Manage your team members, roles, and permissions</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-text-secondary">
                <span className="font-semibold text-text-primary">{company?.activeUsers || 0}</span> / {company?.maxUsers || 0} users
              </div>
              {isAdmin() && canAddUsers() && (
                <button
                  onClick={() => setInviteModalOpen(true)}
                  className="truck-btn-primary flex items-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Invite Member
                </button>
              )}
            </div>
          </div>
        </div>

        {(error || inviteError) && (
          <div className="mx-6 mt-4">
            <Alert
              type="error"
              message={error || inviteError || ''}
              onClose={() => {
                setError(null);
                resetInviteState();
              }}
            />
          </div>
        )}

        {inviteSuccess && (
          <div className="mx-6 mt-4">
            <Alert
              type="success"
              message="Team member invited successfully!"
              onClose={resetInviteState}
            />
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          {/* Table Header */}
          <div className="professional-table-header">
            <div className="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-text-primary">
              <div className="col-span-3">Member</div>
              <div className="col-span-3">Email</div>
              <div className="col-span-2">Role</div>
              <div className="col-span-2">Status</div>
              <div className="col-span-2">Actions</div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border-color">
            {teamMembers.length > 0 ? (
              teamMembers.map((member) => (
                <div
                  key={member._id}
                  className="professional-table-row grid grid-cols-12 gap-4 px-6 py-4 items-center"
                >
                  {/* Member */}
                  <div className="col-span-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary-blue bg-opacity-10 rounded-full flex items-center justify-center">
                        <span className="text-sm font-semibold text-primary-blue">
                          {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-text-primary">{member.name}</p>
                        {member._id === user?._id && (
                          <span className="text-xs text-text-secondary">(You)</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Email */}
                  <div className="col-span-3">
                    <p className="text-text-primary">{member.email}</p>
                  </div>

                  {/* Role */}
                  <div className="col-span-2">
                    <span className={getRoleBadge(member.role)}>
                      {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                    </span>
                  </div>

                  {/* Status */}
                  <div className="col-span-2">
                    <span className={getStatusBadge(member.status || 'active')}>
                      {(member.status || 'active').charAt(0).toUpperCase() + (member.status || 'active').slice(1)}
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="col-span-2">
                    {isAdmin() && member._id !== user?._id && (
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleRemoveMember(member._id)}
                          className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                          title="Remove member"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <EmptyState
                title="No team members found"
                message="Start by inviting team members to collaborate with your dispatch operations"
                actionText={isAdmin() && canAddUsers() ? "Invite Team Member" : undefined}
                onAction={isAdmin() && canAddUsers() ? () => setInviteModalOpen(true) : undefined}
                icon={
                  <svg className="w-10 h-10 text-neutral-gray-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                }
                className="px-6 py-12"
              />
            )}
          </div>
        </div>
      </div>

      <InviteModal
        isOpen={inviteModalOpen}
        onClose={() => setInviteModalOpen(false)}
        onInvite={handleInvite}
        loading={inviteLoading}
      />
    </>
  );
};

export default TeamManagement;
