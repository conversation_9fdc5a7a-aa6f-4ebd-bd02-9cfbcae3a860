import { NavLink, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Sidebar = () => {
  const location = useLocation();
  const { pathname } = location;
  const { isAdmin } = useAuth();

  return (
    <aside className="flex h-screen w-72 flex-col overflow-y-hidden bg-white border-r border-border-color">
      {/* Professional Sidebar Header */}
      <div className="flex items-center gap-3 px-6 py-6 border-b border-border-color">
        <NavLink to="/" className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-primary-blue text-white">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 7h-3V6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h1.05A3.5 3.5 0 0 0 9.5 16.5a3.5 3.5 0 0 0 3.45-2.5h1.1A3.5 3.5 0 0 0 17.5 16.5a3.5 3.5 0 0 0 3.45-2.5H22v-4a3 3 0 0 0-3-3zM9.5 18a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zm8 0a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zM20 12h-3V9h2a1 1 0 0 1 1 1z"/>
            </svg>
          </div>
          <div>
            <h1 className="text-xl font-bold text-text-primary">XTrucker AI</h1>
            <p className="text-xs text-text-secondary">Dispatch Management</p>
          </div>
        </NavLink>
      </div>

      <div className="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
        {/* Professional Navigation */}
        <nav className="flex-1 px-4 py-6">
          {/* Main Navigation */}
          <div className="mb-8">
            <h3 className="mb-4 text-xs font-semibold text-text-secondary uppercase tracking-wider px-3">
              Main Navigation
            </h3>
            <ul className="space-y-1">
              {/* Dashboard */}
              <li>
                <NavLink
                  to="/"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                  Dashboard
                </NavLink>
              </li>

              {/* Load Management */}
              <li>
                <NavLink
                  to="/tables"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 7h-3V6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h1.05A3.5 3.5 0 0 0 9.5 16.5a3.5 3.5 0 0 0 3.45-2.5h1.1A3.5 3.5 0 0 0 17.5 16.5a3.5 3.5 0 0 0 3.45-2.5H22v-4a3 3 0 0 0-3-3zM9.5 18a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zm8 0a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zM20 12h-3V9h2a1 1 0 0 1 1 1z"/>
                  </svg>
                  Load Management
                </NavLink>
              </li>

              {/* Trucking Companies */}
              <li>
                <NavLink
                  to="/trucking-companies"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Trucking Companies
                </NavLink>
              </li>

              {/* Driver Management */}
              <li>
                <NavLink
                  to="/drivers"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Driver Management
                </NavLink>
              </li>

              {/* Load Groups */}
              <li>
                <NavLink
                  to="/load-groups"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  Load Groups
                </NavLink>
              </li>

              {/* Load Management */}
              <li>
                <NavLink
                  to="/loads"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Load Management
                </NavLink>
              </li>

              {/* Call Logs */}
              <li>
                <NavLink
                  to="/call-logs"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  Call Logs
                </NavLink>
              </li>
            </ul>

            {/* Settings Section */}
            <div className="mt-8">
              <h3 className="text-xs font-semibold text-text-secondary uppercase tracking-wider mb-3 px-3">
                Settings
              </h3>
              <ul className="space-y-1">
                {/* Subscription Management - Admin Only */}
                {isAdmin() && (
                  <li>
                    <NavLink
                      to="/subscription"
                      className={({ isActive }) =>
                        `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                          isActive
                            ? 'bg-primary-blue text-white shadow-sm'
                            : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                        }`
                      }
                    >
                      <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                      </svg>
                      Subscription
                    </NavLink>
                  </li>
                )}
              </ul>
            </div>
          </div>

          {/* Account Section */}
          <div className="mb-8">
            <h3 className="mb-4 text-xs font-semibold text-text-secondary uppercase tracking-wider px-3">
              Account
            </h3>
            <ul className="space-y-1">
              {/* Team Management - Admin Only */}
              {isAdmin() && (
                <li>
                  <NavLink
                    to="/team"
                    className={({ isActive }) =>
                      `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                        isActive
                          ? 'bg-primary-blue text-white shadow-sm'
                          : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                      }`
                    }
                  >
                    <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Team Management
                  </NavLink>
                </li>
              )}

              {/* Profile */}
              <li>
                <NavLink
                  to="/profile"
                  className={({ isActive }) =>
                    `group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-blue text-white shadow-sm'
                        : 'text-text-secondary hover:bg-background-light hover:text-text-primary'
                    }`
                  }
                >
                  <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                  Profile Settings
                </NavLink>
              </li>
            </ul>
          </div>

          {/* Quick Actions */}
          <div className="border-t border-border-color pt-6">
            <h3 className="mb-4 text-xs font-semibold text-text-secondary uppercase tracking-wider px-3">
              Quick Actions
            </h3>
            <div className="space-y-2">
              <button className="w-full flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-text-secondary hover:bg-accent-green/10 hover:text-accent-green transition-all duration-200">
                <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add New Load
              </button>
              <button className="w-full flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium text-text-secondary hover:bg-secondary-orange/10 hover:text-secondary-orange transition-all duration-200">
                <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                Make Call
              </button>
            </div>
          </div>
        </nav>

        {/* Footer */}
        <div className="border-t border-border-color p-4">
          <div className="flex items-center gap-3 px-3 py-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-accent-green/10 text-accent-green">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-text-primary truncate">System Status</p>
              <p className="text-xs text-accent-green">All systems operational</p>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
