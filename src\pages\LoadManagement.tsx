import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService, Load, Driver, TruckingCompany } from '../services/api';
import Breadcrumb from '../components/Breadcrumbs/Breadcrumb';

interface AssignLoadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAssign: (truckingCompanyId: string, driverId: string) => void;
  loading: boolean;
  load: Load | null;
  drivers: Driver[];
  truckingCompanies: TruckingCompany[];
}

const AssignLoadModal: React.FC<AssignLoadModalProps> = ({ 
  isOpen, 
  onClose, 
  onAssign, 
  loading, 
  load, 
  drivers, 
  truckingCompanies 
}) => {
  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const [selectedDriverId, setSelectedDriverId] = useState('');

  useEffect(() => {
    if (isOpen) {
      setSelectedCompanyId('');
      setSelectedDriverId('');
    }
  }, [isOpen]);

  const availableDrivers = drivers.filter(driver => 
    driver.available && 
    driver.status === 'active' && 
    (!selectedCompanyId || driver.truckingCompanyId === selectedCompanyId)
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedCompanyId && selectedDriverId) {
      onAssign(selectedCompanyId, selectedDriverId);
    }
  };

  if (!isOpen || !load) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-neutral-gray-dark">
            Assign Load to Driver
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-gray hover:text-neutral-gray-dark"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Load Details */}
        <div className="bg-background-light rounded-lg p-4 mb-6">
          <h4 className="font-semibold text-neutral-gray-dark mb-2">Load Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-text-secondary">Origin:</span>
              <p className="font-medium">{load.origin}</p>
            </div>
            <div>
              <span className="text-text-secondary">Destination:</span>
              <p className="font-medium">{load.destination}</p>
            </div>
            <div>
              <span className="text-text-secondary">Rate:</span>
              <p className="font-medium text-accent-green">${load.rate.toLocaleString()}</p>
            </div>
            <div>
              <span className="text-text-secondary">Distance:</span>
              <p className="font-medium">{load.distanceInMiles} miles</p>
            </div>
            <div>
              <span className="text-text-secondary">Weight:</span>
              <p className="font-medium">{load.weightInPounds.toLocaleString()} lbs</p>
            </div>
            <div>
              <span className="text-text-secondary">Equipment:</span>
              <p className="font-medium">{load.equipment}</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="companyId" className="block text-sm font-medium text-neutral-gray-dark mb-2">
              Select Trucking Company
            </label>
            <select
              id="companyId"
              value={selectedCompanyId}
              onChange={(e) => {
                setSelectedCompanyId(e.target.value);
                setSelectedDriverId(''); // Reset driver selection when company changes
              }}
              required
              className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="">Choose a trucking company</option>
              {truckingCompanies
                .filter(company => company.status === 'active' && company.verified)
                .map((company) => (
                  <option key={company._id} value={company._id}>
                    {company.name} (MC: {company.mcNumber})
                  </option>
                ))}
            </select>
          </div>

          <div>
            <label htmlFor="driverId" className="block text-sm font-medium text-neutral-gray-dark mb-2">
              Select Available Driver
            </label>
            <select
              id="driverId"
              value={selectedDriverId}
              onChange={(e) => setSelectedDriverId(e.target.value)}
              required
              disabled={!selectedCompanyId}
              className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors disabled:bg-neutral-gray-light disabled:cursor-not-allowed"
            >
              <option value="">Choose an available driver</option>
              {availableDrivers.map((driver) => (
                <option key={driver._id} value={driver._id}>
                  {driver.name} - {driver.truckType.replace('_', ' ').toUpperCase()} 
                  {driver.currentLocation && ` (${driver.currentLocation})`}
                </option>
              ))}
            </select>
            {selectedCompanyId && availableDrivers.length === 0 && (
              <p className="text-sm text-red-600 mt-1">No available drivers for this company</p>
            )}
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-border-color rounded-lg text-neutral-gray-dark hover:bg-neutral-gray-light transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !selectedCompanyId || !selectedDriverId}
              className="flex-1 truck-btn-primary flex justify-center items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Assigning...
                </>
              ) : (
                'Assign Load'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const LoadManagement: React.FC = () => {
  const { isAdmin } = useAuth();
  const [loads, setLoads] = useState<Load[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [truckingCompanies, setTruckingCompanies] = useState<TruckingCompany[]>([]);
  const [loading, setLoading] = useState(true);
  const [assignModalOpen, setAssignModalOpen] = useState(false);
  const [selectedLoad, setSelectedLoad] = useState<Load | null>(null);
  const [assignLoading, setAssignLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [companyFilter, setCompanyFilter] = useState('');

  useEffect(() => {
    fetchLoads();
    fetchDrivers();
    fetchTruckingCompanies();
  }, []);

  const fetchLoads = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (statusFilter) params.status = statusFilter;
      if (companyFilter) params.truckingCompanyId = companyFilter;
      
      const response = await apiService.getLoads(params);
      setLoads(response.data.loads || []);
    } catch (error) {
      console.error('Error fetching loads:', error);
      setError('Failed to load loads');
    } finally {
      setLoading(false);
    }
  };

  const fetchDrivers = async () => {
    try {
      const response = await apiService.getDrivers();
      setDrivers(response.data.drivers || []);
    } catch (error) {
      console.error('Error fetching drivers:', error);
    }
  };

  const fetchTruckingCompanies = async () => {
    try {
      const response = await apiService.getTruckingCompanies();
      setTruckingCompanies(response.data.companies || []);
    } catch (error) {
      console.error('Error fetching trucking companies:', error);
    }
  };

  const handleAssignLoad = async (truckingCompanyId: string, driverId: string) => {
    if (!selectedLoad) return;
    
    try {
      setAssignLoading(true);
      await apiService.assignLoad(selectedLoad._id, { truckingCompanyId, driverId });
      setAssignModalOpen(false);
      setSelectedLoad(null);
      fetchLoads();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to assign load');
    } finally {
      setAssignLoading(false);
    }
  };

  const handleUnassignLoad = async (loadId: string) => {
    if (!window.confirm('Are you sure you want to unassign this load?')) return;
    
    try {
      await apiService.unassignLoad(loadId);
      fetchLoads();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to unassign load');
    }
  };

  const handleStatusUpdate = async (loadId: string, status: Load['status']) => {
    try {
      await apiService.updateLoadStatus(loadId, status);
      fetchLoads();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to update load status');
    }
  };

  const handleTriggerAICall = async (loadId: string, driverId: string) => {
    try {
      await apiService.triggerAICall(loadId, driverId);
      // You might want to show a success message or redirect to call logs
      alert('AI call triggered successfully!');
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to trigger AI call');
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-3 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'unassigned':
        return `${baseClasses} bg-yellow-500 bg-opacity-10 text-yellow-600`;
      case 'assigned':
        return `${baseClasses} bg-primary-blue bg-opacity-10 text-primary-blue`;
      case 'in_transit':
        return `${baseClasses} bg-secondary-orange bg-opacity-10 text-secondary-orange`;
      case 'delivered':
        return `${baseClasses} bg-accent-green bg-opacity-10 text-accent-green`;
      case 'cancelled':
        return `${baseClasses} bg-red-500 bg-opacity-10 text-red-600`;
      default:
        return `${baseClasses} bg-neutral-gray bg-opacity-10 text-neutral-gray`;
    }
  };

  const getDriverName = (driverId?: string) => {
    if (!driverId) return 'Unassigned';
    const driver = drivers.find(d => d._id === driverId);
    return driver?.name || 'Unknown Driver';
  };

  const getCompanyName = (companyId?: string) => {
    if (!companyId) return 'Unassigned';
    const company = truckingCompanies.find(c => c._id === companyId);
    return company?.name || 'Unknown Company';
  };

  const filteredLoads = loads.filter(load => {
    const matchesSearch = !searchTerm || 
      load.origin.toLowerCase().includes(searchTerm.toLowerCase()) ||
      load.destination.toLowerCase().includes(searchTerm.toLowerCase()) ||
      load.broker.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || load.status === statusFilter;
    const matchesCompany = !companyFilter || load.truckingCompanyId === companyFilter;
    
    return matchesSearch && matchesStatus && matchesCompany;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-blue rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-neutral-gray-dark mb-2">Loading Loads...</h2>
          <p className="text-neutral-gray">Please wait while we fetch load information</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Breadcrumb pageName="Load Management" />
      
      <div className="professional-table">
        {/* Header */}
        <div className="p-6 border-b border-border-color">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-1">Load Management</h2>
              <p className="text-text-secondary">Manage load assignments, driver selection, and AI call integration</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-text-secondary">
                Total: <span className="font-semibold text-text-primary">{filteredLoads.length}</span> loads
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search loads by origin, destination, or broker..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="">All Status</option>
              <option value="unassigned">Unassigned</option>
              <option value="assigned">Assigned</option>
              <option value="in_transit">In Transit</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <select
              value={companyFilter}
              onChange={(e) => setCompanyFilter(e.target.value)}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="">All Companies</option>
              {truckingCompanies.map((company) => (
                <option key={company._id} value={company._id}>
                  {company.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {error && (
          <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          {/* Table Header */}
          <div className="professional-table-header">
            <div className="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-text-primary">
              <div className="col-span-3">Load Details</div>
              <div className="col-span-2">Rate & Distance</div>
              <div className="col-span-2">Assignment</div>
              <div className="col-span-1">Status</div>
              <div className="col-span-2">Dates</div>
              <div className="col-span-2">Actions</div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border-color">
            {filteredLoads.length > 0 ? (
              filteredLoads.map((load) => (
                <div
                  key={load._id}
                  className="professional-table-row grid grid-cols-12 gap-4 px-6 py-4 items-center"
                >
                  {/* Load Details */}
                  <div className="col-span-3">
                    <div>
                      <p className="font-medium text-text-primary">{load.origin}</p>
                      <p className="text-sm text-text-secondary">to {load.destination}</p>
                      <p className="text-xs text-text-secondary">
                        {load.broker.name} | {load.equipment}
                      </p>
                      <p className="text-xs text-text-secondary">
                        {load.weightInPounds.toLocaleString()} lbs
                      </p>
                    </div>
                  </div>

                  {/* Rate & Distance */}
                  <div className="col-span-2">
                    <div>
                      <p className="font-semibold text-accent-green">${load.rate.toLocaleString()}</p>
                      <p className="text-sm text-text-secondary">{load.distanceInMiles} miles</p>
                      {load.perMileRate && (
                        <p className="text-xs text-text-secondary">
                          ${load.perMileRate.toFixed(2)}/mile
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Assignment */}
                  <div className="col-span-2">
                    <div>
                      <p className="text-sm font-medium text-text-primary">
                        {getCompanyName(load.truckingCompanyId)}
                      </p>
                      <p className="text-sm text-text-secondary">
                        {getDriverName(load.driverId)}
                      </p>
                    </div>
                  </div>

                  {/* Status */}
                  <div className="col-span-1">
                    <span className={getStatusBadge(load.status)}>
                      {load.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>

                  {/* Dates */}
                  <div className="col-span-2">
                    <div>
                      <p className="text-xs text-text-secondary">Pickup:</p>
                      <p className="text-sm font-medium">
                        {new Date(load.pickupDate).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-text-secondary">Delivery:</p>
                      <p className="text-sm font-medium">
                        {new Date(load.deliveryDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="col-span-2">
                    <div className="flex items-center gap-2 flex-wrap">
                      {load.status === 'unassigned' && isAdmin() && (
                        <button
                          onClick={() => {
                            setSelectedLoad(load);
                            setAssignModalOpen(true);
                          }}
                          className="p-2 text-primary-blue hover:bg-primary-blue hover:bg-opacity-10 rounded-lg transition-colors"
                          title="Assign load"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                        </button>
                      )}

                      {load.status === 'assigned' && isAdmin() && (
                        <>
                          <button
                            onClick={() => handleUnassignLoad(load._id)}
                            className="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors"
                            title="Unassign load"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>

                          <button
                            onClick={() => handleStatusUpdate(load._id, 'in_transit')}
                            className="p-2 text-secondary-orange hover:bg-secondary-orange hover:bg-opacity-10 rounded-lg transition-colors"
                            title="Mark in transit"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                          </button>

                          {load.driverId && (
                            <button
                              onClick={() => handleTriggerAICall(load._id, load.driverId!)}
                              className="p-2 text-accent-green hover:bg-accent-green hover:bg-opacity-10 rounded-lg transition-colors"
                              title="Trigger AI call"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                              </svg>
                            </button>
                          )}
                        </>
                      )}

                      {load.status === 'in_transit' && isAdmin() && (
                        <button
                          onClick={() => handleStatusUpdate(load._id, 'delivered')}
                          className="p-2 text-accent-green hover:bg-accent-green hover:bg-opacity-10 rounded-lg transition-colors"
                          title="Mark delivered"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </button>
                      )}

                      {(load.status === 'assigned' || load.status === 'in_transit') && isAdmin() && (
                        <button
                          onClick={() => handleStatusUpdate(load._id, 'cancelled')}
                          className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                          title="Cancel load"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-12 text-center">
                <div className="flex flex-col items-center gap-3">
                  <svg className="w-12 h-12 text-neutral-gray-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary mb-1">No loads found</h3>
                    <p className="text-text-secondary">Loads will appear here when available</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <AssignLoadModal
        isOpen={assignModalOpen}
        onClose={() => {
          setAssignModalOpen(false);
          setSelectedLoad(null);
        }}
        onAssign={handleAssignLoad}
        loading={assignLoading}
        load={selectedLoad}
        drivers={drivers}
        truckingCompanies={truckingCompanies}
      />
    </>
  );
};

export default LoadManagement;
