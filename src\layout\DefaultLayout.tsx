import React, { useState, ReactNode } from 'react';
import { useLocation } from 'react-router-dom';  // Import useLocation
import Header from '../components/Header/index';
import Sidebar from '../components/Sidebar/index';

const DefaultLayout: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation(); // Get current location

  // Conditionally hide the sidebar for specific routes
  const hideSidebar = location.pathname === '/auth/signin' || location.pathname === '/auth/signup';

  return (
    <div className="bg-background-light min-h-screen">
      {/* Professional Page Wrapper */}
      <div className="flex h-screen overflow-hidden">
        {/* Professional Sidebar */}
        {!hideSidebar && (
          <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
        )}

        {/* Main Content Area */}
        <div className="relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden">
          {/* Professional Header */}
          {!hideSidebar && (
            <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />
          )}

          {/* Main Content with Professional Spacing */}
          <main className="flex-1 bg-background-light">
            <div className="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-8">
              <div className="space-y-6">
                {children}
              </div>
            </div>
          </main>
        </div>
      </div>

      {/* Mobile Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-9998 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default DefaultLayout;
