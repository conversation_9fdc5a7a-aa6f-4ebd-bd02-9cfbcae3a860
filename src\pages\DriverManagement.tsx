import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService, Driver, TruckingCompany, DriverCreateData } from '../services/api';
import Breadcrumb from '../components/Breadcrumbs/Breadcrumb';

interface DriverModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: DriverCreateData) => void;
  loading: boolean;
  driver?: Driver | null;
  truckingCompanies: TruckingCompany[];
}

const DriverModal: React.FC<DriverModalProps> = ({ isOpen, onClose, onSave, loading, driver, truckingCompanies }) => {
  const [formData, setFormData] = useState({
    name: '',
    homeBase: '',
    truckingCompanyId: '',
    contactPhone: '',
    contactEmail: '',
    licenseNumber: '',
    licenseType: '',
    hazmatEndorsement: false,
    twicEndorsement: false,
    tankerEndorsement: false,
    licenseExpirationDate: '',
    issuingState: '',
    truckType: '',
    truckMake: '',
    truckModel: '',
    truckYear: '',
    vehicleIdentificationNumber: '',
    plateNumber: '',
    maxWeight: '',
    length: '',
    notes: ''
  });

  // Helper function to format ISO date to YYYY-MM-DD for date input
  const formatDateForInput = (isoDate: string): string => {
    if (!isoDate) return '';
    try {
      const date = new Date(isoDate);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  useEffect(() => {
    if (driver) {
      const companyId = typeof driver.truckingCompanyId === 'string'
        ? driver.truckingCompanyId
        : driver.truckingCompanyId._id;

      setFormData({
        name: driver.name || '',
        homeBase: driver.homeBase || '',
        truckingCompanyId: companyId,
        contactPhone: driver.contactInfo?.phone || '',
        contactEmail: driver.contactInfo?.email || '',
        licenseNumber: driver.license?.number || '',
        licenseType: driver.license?.licenseType || '',
        hazmatEndorsement: driver.license?.endorsements?.hazmat || false,
        twicEndorsement: driver.license?.endorsements?.twic || false,
        tankerEndorsement: driver.license?.endorsements?.tanker || false,
        licenseExpirationDate: formatDateForInput(driver.license?.expirationDate || ''),
        issuingState: driver.license?.issuingState || '',
        truckType: driver.truckSpecs?.truckType || '',
        truckMake: driver.truckSpecs?.make || '',
        truckModel: driver.truckSpecs?.model || '',
        truckYear: driver.truckSpecs?.year?.toString() || '',
        vehicleIdentificationNumber: driver.truckSpecs?.vehicleIdentificationNumber || '',
        plateNumber: driver.truckSpecs?.plateNumber || '',
        maxWeight: driver.truckSpecs?.maxWeight?.toString() || '',
        length: driver.truckSpecs?.length?.toString() || '',
        notes: driver.notes || ''
      });
    } else {
      setFormData({
        name: '',
        homeBase: '',
        truckingCompanyId: '',
        contactPhone: '',
        contactEmail: '',
        licenseNumber: '',
        licenseType: '',
        hazmatEndorsement: false,
        twicEndorsement: false,
        tankerEndorsement: false,
        licenseExpirationDate: '',
        issuingState: '',
        truckType: '',
        truckMake: '',
        truckModel: '',
        truckYear: '',
        vehicleIdentificationNumber: '',
        plateNumber: '',
        maxWeight: '',
        length: '',
        notes: ''
      });
    }
  }, [driver, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const submitData: DriverCreateData = {
      name: formData.name,
      homeBase: formData.homeBase,
      truckingCompanyId: formData.truckingCompanyId,
      contactPhone: formData.contactPhone,
      contactEmail: formData.contactEmail,
      licenseNumber: formData.licenseNumber,
      licenseType: formData.licenseType,
      hazmatEndorsement: formData.hazmatEndorsement,
      twicEndorsement: formData.twicEndorsement,
      tankerEndorsement: formData.tankerEndorsement,
      licenseExpirationDate: formData.licenseExpirationDate,
      issuingState: formData.issuingState,
      truckType: formData.truckType,
      truckMake: formData.truckMake,
      truckModel: formData.truckModel,
      truckYear: formData.truckYear ? parseInt(formData.truckYear) : undefined,
      vehicleIdentificationNumber: formData.vehicleIdentificationNumber,
      plateNumber: formData.plateNumber,
      maxWeight: formData.maxWeight ? parseFloat(formData.maxWeight) : undefined,
      length: formData.length ? parseFloat(formData.length) : undefined,
      notes: formData.notes
    };
    onSave(submitData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      setFormData({ ...formData, [name]: (e.target as HTMLInputElement).checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-neutral-gray-dark">
            {driver ? 'Edit Driver' : 'Add New Driver'}
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-gray hover:text-neutral-gray-dark"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information */}
          <div>
            <h4 className="text-lg font-semibold text-neutral-gray-dark mb-4">Personal Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Driver Name *
                </label>
                <input
                  id="name"
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter driver name"
                  required
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="contactPhone" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Phone Number
                </label>
                <input
                  id="contactPhone"
                  type="tel"
                  name="contactPhone"
                  value={formData.contactPhone}
                  onChange={handleChange}
                  placeholder="Enter phone number"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Email Address
                </label>
                <input
                  id="contactEmail"
                  type="email"
                  name="contactEmail"
                  value={formData.contactEmail}
                  onChange={handleChange}
                  placeholder="Enter email address"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="homeBase" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Home Base *
                </label>
                <input
                  id="homeBase"
                  type="text"
                  name="homeBase"
                  value={formData.homeBase}
                  onChange={handleChange}
                  placeholder="Enter home base city"
                  required
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="truckingCompanyId" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Trucking Company *
                </label>
                <select
                  id="truckingCompanyId"
                  name="truckingCompanyId"
                  value={formData.truckingCompanyId}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                >
                  <option value="">Select trucking company</option>
                  {truckingCompanies.map((company) => (
                    <option key={company._id} value={company._id}>
                      {company.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* License Information */}
          <div>
            <h4 className="text-lg font-semibold text-neutral-gray-dark mb-4">License Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="licenseNumber" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  License Number
                </label>
                <input
                  id="licenseNumber"
                  type="text"
                  name="licenseNumber"
                  value={formData.licenseNumber}
                  onChange={handleChange}
                  placeholder="Enter license number"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="licenseType" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  License Type
                </label>
                <input
                  id="licenseType"
                  type="text"
                  name="licenseType"
                  value={formData.licenseType}
                  onChange={handleChange}
                  placeholder="e.g., CDL Class A"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="licenseExpirationDate" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  License Expiration Date
                </label>
                <input
                  id="licenseExpirationDate"
                  type="date"
                  name="licenseExpirationDate"
                  value={formData.licenseExpirationDate}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="issuingState" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Issuing State
                </label>
                <input
                  id="issuingState"
                  type="text"
                  name="issuingState"
                  value={formData.issuingState}
                  onChange={handleChange}
                  placeholder="e.g., CA, TX, NY"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>
            </div>

            {/* Endorsements */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-neutral-gray-dark mb-3">
                Endorsements
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="hazmatEndorsement"
                    checked={formData.hazmatEndorsement}
                    onChange={handleChange}
                    className="w-4 h-4 text-primary-blue bg-white border-border-color rounded focus:ring-primary-blue focus:ring-2"
                  />
                  <span className="ml-2 text-sm font-medium text-neutral-gray-dark">HAZMAT</span>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="twicEndorsement"
                    checked={formData.twicEndorsement}
                    onChange={handleChange}
                    className="w-4 h-4 text-primary-blue bg-white border-border-color rounded focus:ring-primary-blue focus:ring-2"
                  />
                  <span className="ml-2 text-sm font-medium text-neutral-gray-dark">TWIC</span>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="tankerEndorsement"
                    checked={formData.tankerEndorsement}
                    onChange={handleChange}
                    className="w-4 h-4 text-primary-blue bg-white border-border-color rounded focus:ring-primary-blue focus:ring-2"
                  />
                  <span className="ml-2 text-sm font-medium text-neutral-gray-dark">Tanker</span>
                </div>
              </div>
            </div>
          </div>

          {/* Truck Information */}
          <div>
            <h4 className="text-lg font-semibold text-neutral-gray-dark mb-4">Truck Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="truckType" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Truck Type *
                </label>
                <input
                  id="truckType"
                  type="text"
                  name="truckType"
                  value={formData.truckType}
                  onChange={handleChange}
                  placeholder="e.g., Dry Van, Refrigerated, Flatbed"
                  required
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="truckMake" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Truck Make
                </label>
                <input
                  id="truckMake"
                  type="text"
                  name="truckMake"
                  value={formData.truckMake}
                  onChange={handleChange}
                  placeholder="Enter truck make"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="truckModel" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Truck Model
                </label>
                <input
                  id="truckModel"
                  type="text"
                  name="truckModel"
                  value={formData.truckModel}
                  onChange={handleChange}
                  placeholder="Enter truck model"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="truckYear" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Truck Year
                </label>
                <input
                  id="truckYear"
                  type="number"
                  name="truckYear"
                  value={formData.truckYear}
                  onChange={handleChange}
                  placeholder="Enter truck year"
                  min="1990"
                  max="2030"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="vehicleIdentificationNumber" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  VIN (Vehicle Identification Number)
                </label>
                <input
                  id="vehicleIdentificationNumber"
                  type="text"
                  name="vehicleIdentificationNumber"
                  value={formData.vehicleIdentificationNumber}
                  onChange={handleChange}
                  placeholder="Enter VIN"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="plateNumber" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Plate Number
                </label>
                <input
                  id="plateNumber"
                  type="text"
                  name="plateNumber"
                  value={formData.plateNumber}
                  onChange={handleChange}
                  placeholder="Enter plate number"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="maxWeight" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Max Weight (lbs)
                </label>
                <input
                  id="maxWeight"
                  type="number"
                  name="maxWeight"
                  value={formData.maxWeight}
                  onChange={handleChange}
                  placeholder="Enter max weight"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>

              <div>
                <label htmlFor="length" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                  Length (feet)
                </label>
                <input
                  id="length"
                  type="number"
                  name="length"
                  value={formData.length}
                  onChange={handleChange}
                  placeholder="Enter truck length"
                  className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                />
              </div>
            </div>
          </div>



          {/* Additional Information */}
          <div>
            <h4 className="text-lg font-semibold text-neutral-gray-dark mb-4">Additional Information</h4>
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                placeholder="Enter any additional notes"
                rows={3}
                className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-border-color rounded-lg text-neutral-gray-dark hover:bg-neutral-gray-light transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 truck-btn-primary flex justify-center items-center"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                driver ? 'Update Driver' : 'Add Driver'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const DriverManagement: React.FC = () => {
  const { isAdmin } = useAuth();
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [truckingCompanies, setTruckingCompanies] = useState<TruckingCompany[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [companyFilter, setCompanyFilter] = useState('');

  useEffect(() => {
    fetchDrivers();
    fetchTruckingCompanies();
  }, []);

  const fetchDrivers = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (companyFilter) params.truckingCompanyId = companyFilter;

      const response = await apiService.getDrivers(params);
      setDrivers(response.data.drivers || []);
    } catch (error) {
      console.error('Error fetching drivers:', error);
      setError('Failed to load drivers');
    } finally {
      setLoading(false);
    }
  };

  const fetchTruckingCompanies = async () => {
    try {
      const response = await apiService.getTruckingCompanies();
      setTruckingCompanies(response.data.companies || []);
    } catch (error) {
      console.error('Error fetching trucking companies:', error);
    }
  };

  const handleSave = async (data: DriverCreateData) => {
    try {
      setModalLoading(true);
      if (editingDriver) {
        await apiService.updateDriver(editingDriver._id, data);
      } else {
        await apiService.createDriver(data);
      }
      setModalOpen(false);
      setEditingDriver(null);
      fetchDrivers();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to save driver');
    } finally {
      setModalLoading(false);
    }
  };

  const handleEdit = (driver: Driver) => {
    setEditingDriver(driver);
    setModalOpen(true);
  };

  const handleDelete = async (driverId: string) => {
    if (!window.confirm('Are you sure you want to delete this driver? This action cannot be undone.')) return;

    try {
      await apiService.deleteDriver(driverId);
      fetchDrivers();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to delete driver');
    }
  };

  const getTruckTypeBadge = (truckType: string) => {
    const baseClasses = "px-3 py-1 rounded-full text-xs font-medium";
    switch (truckType) {
      case 'dry_van':
        return `${baseClasses} bg-blue-500 bg-opacity-10 text-blue-600`;
      case 'refrigerated':
        return `${baseClasses} bg-cyan-500 bg-opacity-10 text-cyan-600`;
      case 'flatbed':
        return `${baseClasses} bg-yellow-500 bg-opacity-10 text-yellow-600`;
      case 'tanker':
        return `${baseClasses} bg-purple-500 bg-opacity-10 text-purple-600`;
      case 'car_carrier':
        return `${baseClasses} bg-green-500 bg-opacity-10 text-green-600`;
      case 'heavy_haul':
        return `${baseClasses} bg-red-500 bg-opacity-10 text-red-600`;
      default:
        return `${baseClasses} bg-neutral-gray bg-opacity-10 text-neutral-gray`;
    }
  };



  const getCompanyName = (companyId: string) => {
    const company = truckingCompanies.find(c => c._id === companyId);
    return company?.name || 'Unknown Company';
  };

  const filteredDrivers = drivers.filter(driver => {
    const matchesSearch = !searchTerm ||
      driver.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (driver.contactInfo?.phone && driver.contactInfo.phone.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (driver.contactInfo?.email && driver.contactInfo.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      driver.homeBase.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (driver.license?.number && driver.license.number.toLowerCase().includes(searchTerm.toLowerCase()));

    // Remove status and availability filters since they don't exist in new schema
    const companyId = typeof driver.truckingCompanyId === 'string'
      ? driver.truckingCompanyId
      : driver.truckingCompanyId._id;
    const matchesCompany = !companyFilter || companyId === companyFilter;

    return matchesSearch && matchesCompany;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-blue rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-neutral-gray-dark mb-2">Loading Drivers...</h2>
          <p className="text-neutral-gray">Please wait while we fetch driver information</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Breadcrumb pageName="Driver Management" />

      <div className="professional-table">
        {/* Header */}
        <div className="p-6 border-b border-border-color">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-1">Driver Management</h2>
              <p className="text-text-secondary">Manage drivers, truck details, availability, and location tracking</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-text-secondary">
                Total: <span className="font-semibold text-text-primary">{filteredDrivers.length}</span> drivers
              </div>
              {isAdmin() && (
                <button
                  onClick={() => {
                    setEditingDriver(null);
                    setModalOpen(true);
                  }}
                  className="truck-btn-primary flex items-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Driver
                </button>
              )}
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search drivers, phone, email, home base, or license..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>

            <select
              value={companyFilter}
              onChange={(e) => setCompanyFilter(e.target.value)}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="">All Companies</option>
              {truckingCompanies.map((company) => (
                <option key={company._id} value={company._id}>
                  {company.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {error && (
          <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          {/* Table Header */}
          <div className="professional-table-header">
            <div className="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-text-primary">
              <div className="col-span-4">Driver</div>
              <div className="col-span-3">Company</div>
              <div className="col-span-3">Truck Type</div>
              <div className="col-span-2">License</div>
              <div className="col-span-2">Actions</div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border-color">
            {filteredDrivers.length > 0 ? (
              filteredDrivers.map((driver) => (
                <div
                  key={driver._id}
                  className="professional-table-row grid grid-cols-12 gap-4 px-6 py-4 items-center"
                >
                  {/* Driver */}
                  <div className="col-span-4">
                    <div>
                      <p className="font-medium text-text-primary">{driver.name}</p>
                      <p className="text-sm text-text-secondary">{driver.contactInfo?.phone || 'No phone'}</p>
                      <p className="text-xs text-text-secondary">Home: {driver.homeBase}</p>
                    </div>
                  </div>

                  {/* Company */}
                  <div className="col-span-3">
                    <p className="text-sm font-medium text-text-primary">
                      {typeof driver.truckingCompanyId === 'string'
                        ? getCompanyName(driver.truckingCompanyId)
                        : driver.truckingCompanyId.name}
                    </p>
                  </div>

                  {/* Truck Type */}
                  <div className="col-span-3">
                    <div>
                      <span className={getTruckTypeBadge(driver.truckSpecs?.truckType || 'unknown')}>
                        {driver.truckSpecs?.truckType ? driver.truckSpecs.truckType.replace('_', ' ').toUpperCase() : 'Not specified'}
                      </span>
                      {driver.truckSpecs?.make && driver.truckSpecs?.model && (
                        <p className="text-xs text-text-secondary mt-1">
                          {driver.truckSpecs.make} {driver.truckSpecs.model} {driver.truckSpecs.year}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* License */}
                  <div className="col-span-2">
                    <div>
                      <p className="text-sm text-text-primary">
                        {driver.license?.licenseType || 'Not specified'}
                      </p>
                      {driver.license?.number && (
                        <p className="text-xs text-text-secondary">
                          #{driver.license.number}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="col-span-2">
                    <div className="flex items-center gap-2 justify-end">
                      {isAdmin() && (
                        <>
                          <button
                            onClick={() => handleEdit(driver)}
                            className="p-2 text-primary-blue hover:bg-primary-blue hover:bg-opacity-10 rounded-lg transition-colors"
                            title="Edit driver"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>

                          <button
                            onClick={() => handleDelete(driver._id)}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                            title="Delete driver"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-6 py-12 text-center">
                <div className="flex flex-col items-center gap-3">
                  <svg className="w-12 h-12 text-neutral-gray-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary mb-1">No drivers found</h3>
                    <p className="text-text-secondary">Start by adding drivers to manage your fleet</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <DriverModal
        isOpen={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setEditingDriver(null);
        }}
        onSave={handleSave}
        loading={modalLoading}
        driver={editingDriver}
        truckingCompanies={truckingCompanies}
      />
    </>
  );
};

export default DriverManagement;
