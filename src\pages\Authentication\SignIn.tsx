import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const SignIn: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();
  
  const params = window.location.href.split('?')[1];
  const searchParams = new URLSearchParams(params);
  const extension = searchParams.get('extension');
  const EXTENSION_ID = searchParams.get('extension_id');

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(email, password);

      if (extension) {
        // Send the credentials to the Chrome extension
        if (typeof chrome !== "undefined" && chrome.runtime) {
          console.log("Extension ID: ", EXTENSION_ID);
          chrome.runtime.sendMessage(
            EXTENSION_ID,
            { type: 'login-request' },
            (response) => {
              if (!response?.success) {
                console.log("error sending message", response);
                return response;
              }
              alert("Signed in on extension. You can close this window now.");
              console.log("Success ::: ", response.message);
            }
          );
        } else {
          console.log('chrome.runtime is not available');
        }
      } else {
        // Redirect to the dashboard
        navigate('/dashboard');
      }
    } catch (err: any) {
      console.log(err);
      setError(err.response?.data?.message || 'Invalid credentials');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-gray-light">
      <div className="max-w-6xl w-full mx-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="flex flex-wrap items-center">
            {/* Left Side - Branding */}
            <div className="hidden lg:block lg:w-1/2 bg-primary-blue">
              <div className="p-12 text-center text-white">
                <div className="mb-8">
                  <div className="w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                    </svg>
                  </div>
                  <h1 className="text-3xl font-bold">XTrucker AI</h1>
                  <p className="text-primary-blue-light mt-2">Professional Truck Dispatching</p>
                </div>
                <div className="text-left bg-white bg-opacity-10 rounded-lg p-6">
                  <p className="text-lg leading-relaxed">
                    "XTrucker AI has revolutionized our dispatch process. With AI-driven load matching and automated calls, we've seen a significant increase in efficiency and customer satisfaction."
                  </p>
                  <div className="mt-4 flex items-center">
                    <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold">JD</span>
                    </div>
                    <div className="ml-3">
                      <p className="font-semibold">John Dispatcher</p>
                      <p className="text-sm text-primary-blue-light">Fleet Manager</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="w-full lg:w-1/2">
              <div className="p-8 lg:p-12">
                <div className="mb-8">
                  <h2 className="text-3xl font-bold text-neutral-gray-dark mb-2">
                    Welcome Back
                  </h2>
                  <p className="text-neutral-gray">Sign in to your dispatcher account</p>
                </div>

                {error && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-red-600 text-sm">{error}</p>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Email Address
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-neutral-gray-dark mb-2">
                      Password
                    </label>
                    <input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter your password"
                      required
                      className="w-full px-4 py-3 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <input
                        id="remember-me"
                        type="checkbox"
                        className="h-4 w-4 text-primary-blue focus:ring-primary-blue border-border-color rounded"
                      />
                      <label htmlFor="remember-me" className="ml-2 block text-sm text-neutral-gray">
                        Remember me
                      </label>
                    </div>
                    <Link to="/forgot-password" className="text-sm font-medium text-primary-blue hover:text-primary-blue-dark">
                      Forgot password?
                    </Link>
                  </div>

                  <button
                    type="submit"
                    disabled={loading}
                    className="truck-btn-primary w-full flex justify-center items-center"
                  >
                    {loading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Signing in...
                      </>
                    ) : (
                      'Sign In'
                    )}
                  </button>
                </form>

                <div className="mt-8 text-center">
                  <p className="text-sm text-neutral-gray">
                    Don't have an account?{' '}
                    <Link to="/auth/signup" className="font-medium text-primary-blue hover:text-primary-blue-dark">
                      Sign up for free
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
