import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService, Load, Driver, TruckingCompany, CallLog } from '../../services/api';
import CardDataStats from '../../components/CardDataStats';
import ChartOne from '../../components/Charts/ChartOne';
import ChartThree from '../../components/Charts/ChartThree';
import ChartTwo from '../../components/Charts/ChartTwo';

interface DashboardStats {
  activeLoads: number;
  totalRevenue: number;
  fleetUtilization: number;
  driverPerformance: number;
  totalDrivers: number;
  totalCompanies: number;
  recentCalls: number;
}

const ECommerce: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    activeLoads: 0,
    totalRevenue: 0,
    fleetUtilization: 0,
    driverPerformance: 0,
    totalDrivers: 0,
    totalCompanies: 0,
    recentCalls: 0,
  });
  const [loading, setLoading] = useState(true);
  const [recentLoads, setRecentLoads] = useState<Load[]>([]);
  const [recentCalls, setRecentCalls] = useState<CallLog[]>([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch all data in parallel
      const [loadsResponse, driversResponse, companiesResponse] = await Promise.all([
        apiService.getLoads({ limit: 50 }),
        apiService.getDrivers({ limit: 100 }),
        apiService.getTruckingCompanies({ limit: 100 })
      ]);

      const loads = loadsResponse.data || [];
      const drivers = driversResponse.data || [];
      const companies = companiesResponse.data || [];

      // Calculate statistics
      const activeLoads = loads.filter((load: Load) =>
        load.status === 'assigned' || load.status === 'in_transit'
      ).length;

      const totalRevenue = loads
        .filter((load: Load) => load.status === 'delivered')
        .reduce((sum: number, load: Load) => sum + load.rate, 0);

      const availableDrivers = drivers.filter((driver: Driver) => driver.available).length;
      const fleetUtilization = drivers.length > 0 ? (availableDrivers / drivers.length) * 100 : 0;

      const activeDrivers = drivers.filter((driver: Driver) => driver.status === 'active').length;
      const driverPerformance = drivers.length > 0 ? (activeDrivers / drivers.length) * 100 : 0;

      setStats({
        activeLoads,
        totalRevenue,
        fleetUtilization,
        driverPerformance,
        totalDrivers: drivers.length,
        totalCompanies: companies.length,
        recentCalls: 0, // Will be updated when call logs are fetched
      });

      // Set recent loads (last 5)
      setRecentLoads(loads.slice(0, 5));

      // Try to fetch recent call logs (optional, may fail if not implemented)
      try {
        const callLogsResponse = await apiService.getCallLogs({ limit: 5 });
        const callLogs = callLogsResponse.data || [];
        setRecentCalls(callLogs);
        setStats(prev => ({ ...prev, recentCalls: callLogs.length }));
      } catch (error) {
        console.log('Call logs not available:', error);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Add New Load',
      description: 'Create a new load assignment',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      action: () => navigate('/loads'),
      color: 'bg-primary-blue',
      adminOnly: false,
    },
    {
      title: 'Manage Drivers',
      description: 'View and manage driver information',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      action: () => navigate('/drivers'),
      color: 'bg-accent-green',
      adminOnly: false,
    },
    {
      title: 'View Call Logs',
      description: 'Review AI call conversations',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
      ),
      action: () => navigate('/call-logs'),
      color: 'bg-secondary-orange',
      adminOnly: false,
    },
    {
      title: 'Manage Team',
      description: 'Add and manage team members',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
        </svg>
      ),
      action: () => navigate('/team'),
      color: 'bg-purple-500',
      adminOnly: true,
    },
  ];

  const filteredQuickActions = quickActions.filter(action =>
    !action.adminOnly || isAdmin()
  );

  return (
    <>
      {/* Professional Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-text-primary mb-2">
              Welcome back, {user?.name || 'User'}
            </h1>
            <p className="text-text-secondary">
              Monitor your fleet performance, load management, and operational metrics in real-time
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-text-secondary">
              Role: <span className="font-medium text-text-primary capitalize">{user?.role}</span>
            </p>
            <p className="text-sm text-text-secondary">
              Last updated: {new Date().toLocaleTimeString()}
            </p>
          </div>
        </div>
      </div>

      {/* Professional Metrics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4 mb-8">
        <CardDataStats
          title="Active Loads"
          total={loading ? "..." : stats.activeLoads.toString()}
          rate={loading ? "..." : "Real-time"}
          levelUp={!loading}
          subtitle="Currently in transit"
          iconBgColor="blue"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M19 7h-3V6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h1.05A3.5 3.5 0 0 0 9.5 16.5a3.5 3.5 0 0 0 3.45-2.5h1.1A3.5 3.5 0 0 0 17.5 16.5a3.5 3.5 0 0 0 3.45-2.5H22v-4a3 3 0 0 0-3-3zM9.5 18a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zm8 0a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zM20 12h-3V9h2a1 1 0 0 1 1 1z"/>
          </svg>
        </CardDataStats>

        <CardDataStats
          title="Total Revenue"
          total={loading ? "..." : `$${(stats.totalRevenue / 1000).toFixed(1)}K`}
          rate={loading ? "..." : "Delivered loads"}
          levelUp={!loading && stats.totalRevenue > 0}
          subtitle="From completed loads"
          iconBgColor="green"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"/>
          </svg>
        </CardDataStats>

        <CardDataStats
          title="Fleet Utilization"
          total={loading ? "..." : `${stats.fleetUtilization.toFixed(1)}%`}
          rate={loading ? "..." : `${stats.totalDrivers} drivers`}
          levelUp={!loading && stats.fleetUtilization > 80}
          subtitle="Available drivers"
          iconBgColor="orange"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10zM7 9a1 1 0 1 1-1 1 1 1 0 0 1 1-1zm5 7H9.41l.64-.64a1 1 0 0 0 0-1.41 1 1 0 0 0-1.41 0l-2.64 2.64a1 1 0 0 0 0 1.41L8.64 20a1 1 0 0 0 1.41 0 1 1 0 0 0 0-1.41L9.41 18H12a1 1 0 0 0 0-2z"/>
          </svg>
        </CardDataStats>

        <CardDataStats
          title="Driver Performance"
          total={loading ? "..." : `${stats.driverPerformance.toFixed(1)}%`}
          rate={loading ? "..." : `${stats.totalCompanies} companies`}
          levelUp={!loading && stats.driverPerformance > 90}
          subtitle="Active drivers"
          iconBgColor="gray"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </CardDataStats>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-text-primary mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {filteredQuickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="p-4 bg-white rounded-lg border border-border-color hover:shadow-lg transition-all duration-200 text-left group"
            >
              <div className="flex items-center mb-3">
                <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center text-white mr-3 group-hover:scale-110 transition-transform duration-200`}>
                  {action.icon}
                </div>
                <div>
                  <h3 className="font-semibold text-text-primary group-hover:text-primary-blue transition-colors">
                    {action.title}
                  </h3>
                </div>
              </div>
              <p className="text-sm text-text-secondary">{action.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Recent Loads */}
        <div className="bg-white rounded-lg border border-border-color p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-text-primary">Recent Loads</h3>
            <button
              onClick={() => navigate('/loads')}
              className="text-primary-blue hover:text-primary-blue/80 text-sm font-medium"
            >
              View All
            </button>
          </div>

          {loading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-neutral-gray bg-opacity-20 rounded mb-2"></div>
                  <div className="h-3 bg-neutral-gray bg-opacity-20 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : recentLoads.length > 0 ? (
            <div className="space-y-3">
              {recentLoads.map((load) => (
                <div key={load._id} className="flex items-center justify-between p-3 bg-background-light rounded-lg">
                  <div>
                    <p className="font-medium text-text-primary text-sm">
                      {load.origin} → {load.destination}
                    </p>
                    <p className="text-xs text-text-secondary">
                      {load.broker.name} • ${load.rate.toLocaleString()}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    load.status === 'delivered' ? 'bg-accent-green bg-opacity-10 text-accent-green' :
                    load.status === 'in_transit' ? 'bg-secondary-orange bg-opacity-10 text-secondary-orange' :
                    load.status === 'assigned' ? 'bg-primary-blue bg-opacity-10 text-primary-blue' :
                    'bg-neutral-gray bg-opacity-10 text-neutral-gray'
                  }`}>
                    {load.status.replace('_', ' ')}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-text-secondary text-sm">No recent loads found</p>
          )}
        </div>

        {/* Recent Call Logs */}
        <div className="bg-white rounded-lg border border-border-color p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-text-primary">Recent AI Calls</h3>
            <button
              onClick={() => navigate('/call-logs')}
              className="text-primary-blue hover:text-primary-blue/80 text-sm font-medium"
            >
              View All
            </button>
          </div>

          {loading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-neutral-gray bg-opacity-20 rounded mb-2"></div>
                  <div className="h-3 bg-neutral-gray bg-opacity-20 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : recentCalls.length > 0 ? (
            <div className="space-y-3">
              {recentCalls.map((call) => (
                <div key={call.id} className="flex items-center justify-between p-3 bg-background-light rounded-lg">
                  <div>
                    <p className="font-medium text-text-primary text-sm">
                      {call.type === 'inboundPhoneCall' ? 'Inbound' : 'Outbound'} Call
                    </p>
                    <p className="text-xs text-text-secondary">
                      {new Date(call.startedAt).toLocaleString()}
                      {call.costBreakdown && ` • $${call.costBreakdown.total.toFixed(4)}`}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    call.status === 'ended' ? 'bg-accent-green bg-opacity-10 text-accent-green' :
                    call.status === 'started' ? 'bg-secondary-orange bg-opacity-10 text-secondary-orange' :
                    'bg-red-500 bg-opacity-10 text-red-600'
                  }`}>
                    {call.status}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-text-secondary text-sm">No recent calls found</p>
          )}
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-12 gap-6">
        <ChartOne />
        <ChartTwo />
        <ChartThree />
      </div>
    </>
  );
};

export default ECommerce;
