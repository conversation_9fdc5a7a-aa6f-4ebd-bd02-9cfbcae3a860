# Use the official AWS Lambda Node.js 18 base image
FROM public.ecr.aws/lambda/nodejs:18

# Copy the package.json and package-lock.json files
COPY package*.json ./

# Install dependencies
RUN npm install

# Install aws-lambda-ric
RUN npm install aws-lambda-ric

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Set the CMD to use the aws-lambda-ric and point to your Lambda function handler
CMD ["node_modules/aws-lambda-ric/bin/index.js", "dist/index.handler"]