@tailwind base;
@tailwind components;
@tailwind utilities;

/* Root Variables - Truck Dispatching Theme */
:root {
  --primary-blue: #1e40af;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1e3a8a;
  --secondary-orange: #ea580c;
  --secondary-orange-light: #f97316;
  --accent-green: #059669;
  --accent-green-light: #10b981;
  --neutral-gray: #6b7280;
  --neutral-gray-light: #9ca3af;
  --neutral-gray-dark: #374151;
  --background-light: #f8fafc;
  --background-white: #ffffff;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --border-radius-lg: 12px;
}

@layer base {
  body {
    @apply font-satoshi font-normal text-base text-text-secondary bg-background-light relative z-1;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-text-primary font-semibold;
  }

  .truck-card {
    @apply bg-white rounded-lg shadow-md border border-border-color transition-all duration-200 hover:shadow-lg;
  }

  .truck-btn-primary {
    @apply bg-primary-blue text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-primary-blue-dark focus:ring-2 focus:ring-primary-blue-light focus:ring-opacity-50;
  }

  .truck-btn-secondary {
    @apply bg-secondary-orange text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-secondary-orange-light focus:ring-2 focus:ring-secondary-orange focus:ring-opacity-50;
  }

  .truck-status-active {
    @apply bg-accent-green text-white px-3 py-1 rounded-full text-sm font-medium;
  }

  .truck-status-pending {
    @apply bg-secondary-orange text-white px-3 py-1 rounded-full text-sm font-medium;
  }

  .truck-status-inactive {
    @apply bg-neutral-gray text-white px-3 py-1 rounded-full text-sm font-medium;
  }
}

@layer components {
  .professional-table {
    @apply bg-white rounded-lg shadow-sm border border-border-color overflow-hidden;
  }

  .professional-table-header {
    @apply bg-background-light border-b border-border-color;
  }

  .professional-table-row {
    @apply border-b border-border-color hover:bg-gray-50 transition-colors duration-150 cursor-pointer;
  }

  .professional-card {
    @apply bg-white rounded-lg shadow-sm border border-border-color p-6 transition-all duration-200 hover:shadow-md;
  }

  .professional-metric-card {
    @apply bg-white rounded-lg shadow-sm border border-border-color p-6 transition-all duration-200 hover:shadow-md hover:border-primary-blue-light;
  }

  /* Professional Button Styles */
  .truck-btn-primary {
    @apply bg-primary-blue text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:bg-primary-blue-dark hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-blue focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .truck-btn-secondary {
    @apply bg-white text-primary-blue border border-primary-blue px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:bg-primary-blue hover:text-white focus:outline-none focus:ring-2 focus:ring-primary-blue focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .truck-btn-success {
    @apply bg-accent-green text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:bg-accent-green-dark hover:shadow-md focus:outline-none focus:ring-2 focus:ring-accent-green focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .truck-btn-warning {
    @apply bg-secondary-orange text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:bg-secondary-orange-light hover:shadow-md focus:outline-none focus:ring-2 focus:ring-secondary-orange focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Professional Loading States */
  .truck-loading-spinner {
    @apply inline-block w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin;
  }

  .truck-loading-dots {
    @apply flex items-center gap-1;
  }

  .truck-loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
    animation-delay: calc(var(--i) * 0.2s);
  }

  .truck-skeleton {
    @apply bg-neutral-gray-light rounded animate-pulse;
  }

  .truck-skeleton-text {
    @apply h-4 bg-neutral-gray-light rounded animate-pulse;
  }

  .truck-skeleton-avatar {
    @apply w-10 h-10 bg-neutral-gray-light rounded-full animate-pulse;
  }

  /* Professional Error States */
  .truck-error-card {
    @apply bg-red-50 border border-red-200 rounded-lg p-4 text-red-800;
  }

  .truck-success-card {
    @apply bg-green-50 border border-green-200 rounded-lg p-4 text-green-800;
  }

  .truck-warning-card {
    @apply bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-yellow-800;
  }

  .truck-info-card {
    @apply bg-blue-50 border border-blue-200 rounded-lg p-4 text-blue-800;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .chat-height {
    @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
  }
  .inbox-height {
    @apply h-[calc(100vh_-_8.125rem)] lg:h-[calc(100vh_-_5.625rem)];
  }
}

/* third-party libraries CSS */

.tableCheckbox:checked ~ div span {
  @apply opacity-100;
}
.tableCheckbox:checked ~ div {
  @apply bg-primary border-primary;
}

.apexcharts-legend-text {
  @apply !text-body dark:!text-bodydark;
}
.apexcharts-text {
  @apply !fill-body dark:!fill-bodydark;
}
.apexcharts-xcrosshairs {
  @apply !fill-stroke dark:!fill-strokedark;
}
.apexcharts-gridline {
  @apply !stroke-stroke dark:!stroke-strokedark;
}
.apexcharts-series.apexcharts-pie-series path {
  @apply dark:!stroke-transparent;
}
.apexcharts-legend-series {
  @apply !inline-flex gap-1.5;
}
.apexcharts-tooltip.apexcharts-theme-light {
  @apply dark:!border-strokedark dark:!bg-boxdark;
}
.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  @apply dark:!border-strokedark dark:!bg-meta-4;
}
.apexcharts-xaxistooltip,
.apexcharts-yaxistooltip {
  @apply dark:!border-meta-4 dark:!bg-meta-4 dark:!text-bodydark1;
}
.apexcharts-xaxistooltip-bottom:after {
  @apply !border-b-gray dark:!border-b-meta-4;
}
.apexcharts-xaxistooltip-bottom:before {
  @apply !border-b-gray dark:!border-b-meta-4;
}
.apexcharts-xaxistooltip-bottom {
  @apply !rounded !border-none !bg-gray !text-xs !font-medium !text-black dark:!text-white;
}
.apexcharts-tooltip-series-group {
  @apply !pl-1.5;
}

.flatpickr-wrapper {
  @apply w-full;
}
.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  @apply !fill-primary;
}
.flatpickr-calendar.arrowTop:before {
  @apply dark:!border-b-boxdark;
}
.flatpickr-calendar.arrowTop:after {
  @apply dark:!border-b-boxdark;
}
.flatpickr-calendar {
  @apply !p-6 dark:!bg-boxdark dark:!text-bodydark dark:!shadow-8 2xsm:!w-auto;
}
.flatpickr-day {
  @apply dark:!text-bodydark dark:hover:!border-meta-4 dark:hover:!bg-meta-4;
}
.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply !top-7 dark:!fill-white dark:!text-white;
}
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  @apply !left-7;
}
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  @apply !right-7;
}
span.flatpickr-weekday,
.flatpickr-months .flatpickr-month {
  @apply dark:!fill-white dark:!text-white;
}
.flatpickr-day.inRange {
  @apply dark:!shadow-7;
  box-shadow: -5px 0 0 #EFF4FB, 5px 0 0 #EFF4FB;
}
.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  @apply border-gray bg-gray dark:!border-meta-4 dark:!bg-meta-4;
}
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.selected,
.flatpickr-day.endRange {
  @apply dark:!text-white;
}
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #3c50e0;
  @apply !border-primary !bg-primary hover:!border-primary hover:!bg-primary;
}
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
  box-shadow: -10px 0 0 #3c50e0;
}

.map-btn .jvm-zoom-btn {
  @apply flex items-center justify-center w-7.5 h-7.5 rounded border border-stroke dark:border-strokedark hover:border-primary dark:hover:border-primary bg-white hover:bg-primary text-body hover:text-white dark:text-bodydark dark:hover:text-white text-2xl leading-none px-0 pt-0 pb-0.5;
}
.mapOne .jvm-zoom-btn {
  @apply left-auto top-auto bottom-0;
}
.mapOne .jvm-zoom-btn.jvm-zoomin {
  @apply right-10;
}
.mapOne .jvm-zoom-btn.jvm-zoomout {
  @apply right-0;
}
.mapTwo .jvm-zoom-btn {
  @apply top-auto bottom-0;
}
.mapTwo .jvm-zoom-btn.jvm-zoomin {
  @apply left-0;
}
.mapTwo .jvm-zoom-btn.jvm-zoomout {
  @apply left-10;
}

.taskCheckbox:checked ~ .box span {
  @apply opacity-100;
}
.taskCheckbox:checked ~ p {
  @apply line-through;
}
.taskCheckbox:checked ~ .box {
  @apply bg-primary border-primary dark:border-primary;
}

.custom-input-date::-webkit-calendar-picker-indicator {
  background-position: center;
  background-repeat: no-repeat;
  background-size: 20px;
}
.custom-input-date-1::-webkit-calendar-picker-indicator {
  background-image: url(./images/icon/icon-calendar.svg);
}
.custom-input-date-2::-webkit-calendar-picker-indicator {
  background-image: url(./images/icon/icon-arrow-down.svg);
}

[x-cloak] {
  display: none !important;
}