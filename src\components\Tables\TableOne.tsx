import axios from 'axios';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { BASE_URL } from '../../lib/constants';



// Define types for Call Log and User
interface CallLog {
  cost: number;
  type: string;
  status: string;
  customer: any;
  _id: string;
  createdAt: string;
  duration: number;
  callerName: string;
  id: string; // Call Id
}

interface User {
  id: string;
  email: string;
  name: string;
  profile_image: string;
}

const TableOne = () => {

  const [callLogs, setCallLogs] = useState<CallLog[]>([]); // Type for call logs
  let navigate = useNavigate();

  useEffect(() => {
    // Retrieve user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      console.error('User not found in localStorage');
      return;
    }

    // Parse the user object
    const user: User = JSON.parse(storedUser);
    const userId = user?.id;

    if (!userId) {
      console.error('User ID not found');
      return;
    }

    // Fetch call logs by user ID
    const fetchCallLogs = async () => {
      try {
        const response = await axios.get<CallLog[]>(`${BASE_URL}/loads/call-logs/user/${userId}`);
        setCallLogs(response.data);
      } catch (error) {
        console.error('Error fetching call logs:', error);
      }
    };

    fetchCallLogs();
  }, []);

  // Handle row click to navigate to detailed call log page
  const handleRowClick = (callLogId: string) => {
    navigate(`/call-log/${callLogId}`); // Navigate to detailed call log page
  };


  return (
    <div className="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
      <h4 className="mb-6 text-xl font-semibold text-black dark:text-white">Call Logs</h4>
      <div className="flex flex-col">
        <div className="grid grid-cols-3  rounded-sm bg-gray-2 dark:bg-meta-4 sm:grid-cols-5">
          <div className="p-2.5 xl:p-5">
            <h5 className="text-sm font-medium  xsm:text-base">Date</h5>
          </div>
          {/* <div className="p-2.5 xl:p-5">
            <h5 className="text-sm font-medium  xsm:text-base">Status</h5>
          </div> */}
          <div className="p-2.5 xl:p-5">
            <h5 className="text-sm font-medium  xsm:text-base">Caller Number</h5>
          </div>
          <div className="p-2.5 xl:p-5">
            <h5 className="text-sm font-medium  xsm:text-base">Type</h5>
          </div>
          {/* <div className="p-2.5 xl:p-5">
            <h5 className="text-sm font-medium  xsm:text-base">Cost</h5>
          </div> */}
        </div>

        {callLogs.map((callLog) => (
          <div
            key={callLog.id}
            className="grid grid-cols-3 sm:grid-cols-5 border-b border-stroke dark:border-strokedark cursor-pointer"
            onClick={() => handleRowClick(callLog.id)}
          >
            <div className="flex  gap-3 p-2.5 xl:p-5">
              <p className="text-black dark:text-white">{new Date(callLog?.createdAt).toLocaleDateString()}</p>
            </div>
            {/* <div className="flex   p-2.5 xl:p-5">
              <p className="text-black dark:text-white">{callLog?.status} </p>
            </div> */}
            <div className="flex   p-2.5 xl:p-5">
              <p className="text-black dark:text-white">{callLog?.customer?.number}</p>
            </div>
            <div className="flex   p-2.5 xl:p-5">
              <p className="text-black dark:text-white">{callLog?.type}</p>
            </div>
            {/* <div className="flex   p-2.5 xl:p-5">
              <p className="text-black dark:text-white">{callLog?.cost}</p>
            </div> */}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TableOne;
