import axios from 'axios';
import { useEffect, useState } from 'react';
import { BASE_URL } from '../../lib/constants';

interface User {
  id: string;
  email: string;
  name: string;
  profile_image: string;
}

// Represents information about the company related to the load
interface CompanyInformation {
  address: string;          // Company's address
  mcNumber: string;         // Motor Carrier number
  usdotNumber: string;      // USDOT number
  creditRating: string;     // Credit rating details


  call: string;             // Contact phone number
}

// Represents the delivery instructions for the load
interface DeliveryInstructions {
  deliveryInstructions: string; // Detailed delivery instructions
}

// Represents details about the load itself
interface LoadsDetails {
  location: string;               // Pickup location
  destination: string;            // Delivery destination
  dateAndTime: string;            // Date and time for pickup/delivery
  drivingTime: string;            // Estimated driving time for the load
  trailerType: string;            // Type of trailer required for the load
  loadSize: string;               // Size category of the load (e.g., TL, LTL)
  weight: string;                 // Weight of the load
  postedRate: string;             // Rate posted for the load
  marketRateEstimate: string;     // Estimated market rate for comparison
  pricePerMile: string;     // Price per mile for the load
  price: string;            // Total price for the load
  deadhead: string;         // Distance without a load (deadhead miles)
  company: string;           // Name of the company
  mileage: string;          // Total mileage for the load
}

// Represents a load object containing various details
interface Load {
  _id: string;                     // Unique identifier for the load
  companyInformation: CompanyInformation; // Company information related to the load
  deliveryInstructions: DeliveryInstructions; // Instructions for delivery
  loadsDetails: LoadsDetails;      // Specific details about the load
  userId: string;                  // ID of the user associated with the load
  platformName: string;            // URL to the load on the platform
  id: string;                      // Another identifier (could be for different purposes)
  createdAt: string;               // Timestamp of when the load was created
  updatedAt: string;               // Timestamp of the last update to the load
  __v: number;                     // Version key (typically used by MongoDB)
}


const TableTwo = () => {
  const [loads, setLoads] = useState<Load[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      console.error('User not found in localStorage');
      return;
    }

    const user: User = JSON.parse(storedUser);
    const userId = user?.id;

    if (!userId) {
      console.error('User ID not found');
      return;
    }

    const fetchLoads = async () => {
      try {
        const response = await axios.get(`${BASE_URL}/loads/current/user/${userId}`);
        console.log(response.data); // Log the response for debugging
        setLoads(Array.isArray(response.data.currentLoads) ? response.data.currentLoads : []);
      } catch (err: any) {
        setError(`Failed to fetch loads: ${err?.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchLoads();
  }, []);

  if (loading) return <div className="flex justify-center items-center h-64"><div className="spinner">Loading...</div></div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {Array.isArray(loads) && loads.length > 0 ? (
        loads.map((load) => (
          <div key={load._id} className="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark my-4">
            {/* Header */}
            <div className="py-6 px-4 md:px-6 xl:px-7.5">
              <h4 className="text-xl font-semibold text-black dark:text-white">
                Load from {load.loadsDetails.company}
              </h4>
            </div>

            {/* Load Details */}
            <div className="grid grid-cols-2 border-t border-stroke py-4.5 px-4 dark:border-strokedark sm:grid-cols-4 md:px-6 2xl:px-7.5">
              {/* Company Information */}
              <div className="col-span-1">
                <p className="font-bold">Company:</p>
                <p>{load.loadsDetails.company}</p>
              </div>
              <div className="col-span-1">
                <p className="font-bold">MC Number:</p>
                <p>{load.companyInformation.mcNumber}</p>
              </div>
              <div className="col-span-1">
                <p className="font-bold">USDOT Number:</p>
                <p>{load.companyInformation.usdotNumber}</p>
              </div>
              <div className="col-span-1">
                <p className="font-bold">Mileage:</p>
                <p>{load.loadsDetails.mileage}</p>
              </div>
              <div className="col-span-1">
                <p className="font-bold">Price:</p>
                <p>{load.loadsDetails.price}</p>
              </div>
              <div className="col-span-1">
                <p className="font-bold">Credit Rating:</p>
                <p>{load.companyInformation.creditRating}</p>
              </div>
              <div className="col-span-1">
                <p className="font-bold">Deadhead:</p>
                <p>{load.loadsDetails.deadhead}</p>
              </div>
              <div className="col-span-1">
                <p className="font-bold">Price Per Mile:</p>
                <p>{load.loadsDetails.pricePerMile}</p>
              </div>

              {/* Load Specific Details */}
              <div className="col-span-2 border-t border-stroke dark:border-strokedark pt-4 mt-4">
                <p className="font-bold">Load Details:</p>
                <p>Location: {load.loadsDetails.location}</p>
                <p>Destination: {load.loadsDetails.destination}</p>
                <p>Date & Time: {new Date(load?.createdAt).toLocaleDateString()}</p>
                <p>Trailer Type: {load.loadsDetails.trailerType}</p>
                <p>Weight: {load.loadsDetails.weight}</p>
                <p>Load Size: {load.loadsDetails.loadSize}</p>
               
              </div>

              {/* Delivery Instructions */}
              <div className="col-span-2 border-t border-stroke dark:border-strokedark pt-4 mt-4">
                <p className="font-bold">Delivery Instructions:</p>
                <p>{load.deliveryInstructions.deliveryInstructions}</p>
              </div>
            </div>
          </div>
        ))
      ) : (
        <p>No loads available.</p>
      )}
    </div>

  );
};

export default TableTwo;
