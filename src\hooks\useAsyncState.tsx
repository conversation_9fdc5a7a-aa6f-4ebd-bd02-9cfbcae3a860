import { useState, useCallback, useEffect } from 'react';

// Generic async state management hook
interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastFetch: Date | null;
}

interface UseAsyncStateOptions {
  initialData?: any;
  autoFetch?: boolean;
  cacheTime?: number; // in milliseconds
}

interface UseAsyncStateReturn<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastFetch: Date | null;
  execute: (asyncFunction: () => Promise<T>) => Promise<void>;
  retry: () => Promise<void>;
  reset: () => void;
  setData: (data: T | null) => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
}

export function useAsyncState<T = any>(
  asyncFunction?: () => Promise<T>,
  options: UseAsyncStateOptions = {}
): UseAsyncStateReturn<T> {
  const { initialData = null, autoFetch = false, cacheTime = 0 } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null,
    lastFetch: null,
  });

  const [lastAsyncFunction, setLastAsyncFunction] = useState<(() => Promise<T>) | null>(null);

  const execute = useCallback(async (fn: () => Promise<T>) => {
    // Check cache validity
    if (cacheTime > 0 && state.lastFetch) {
      const timeSinceLastFetch = Date.now() - state.lastFetch.getTime();
      if (timeSinceLastFetch < cacheTime && state.data !== null) {
        return; // Use cached data
      }
    }

    setState(prev => ({ ...prev, loading: true, error: null }));
    setLastAsyncFunction(() => fn);

    try {
      const result = await fn();
      setState(prev => ({
        ...prev,
        data: result,
        loading: false,
        error: null,
        lastFetch: new Date(),
      }));
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || error?.message || 'An unexpected error occurred';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        lastFetch: new Date(),
      }));
    }
  }, [cacheTime, state.lastFetch, state.data]);

  const retry = useCallback(async () => {
    if (lastAsyncFunction) {
      await execute(lastAsyncFunction);
    }
  }, [execute, lastAsyncFunction]);

  const reset = useCallback(() => {
    setState({
      data: initialData,
      loading: false,
      error: null,
      lastFetch: null,
    });
    setLastAsyncFunction(null);
  }, [initialData]);

  const setData = useCallback((data: T | null) => {
    setState(prev => ({ ...prev, data }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error, loading: false }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch && asyncFunction) {
      execute(asyncFunction);
    }
  }, [autoFetch, asyncFunction, execute]);

  return {
    data: state.data,
    loading: state.loading,
    error: state.error,
    lastFetch: state.lastFetch,
    execute,
    retry,
    reset,
    setData,
    setError,
    setLoading,
  };
}

// Hook for managing multiple async operations
interface UseMultiAsyncStateReturn {
  loading: boolean;
  errors: Record<string, string | null>;
  execute: (key: string, asyncFunction: () => Promise<any>) => Promise<void>;
  setError: (key: string, error: string | null) => void;
  clearErrors: () => void;
  isLoading: (key: string) => boolean;
  getError: (key: string) => string | null;
}

export function useMultiAsyncState(): UseMultiAsyncStateReturn {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, string | null>>({});

  const execute = useCallback(async (key: string, asyncFunction: () => Promise<any>) => {
    setLoadingStates(prev => ({ ...prev, [key]: true }));
    setErrors(prev => ({ ...prev, [key]: null }));

    try {
      const result = await asyncFunction();
      setLoadingStates(prev => ({ ...prev, [key]: false }));
      return result;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || error?.message || 'An unexpected error occurred';
      setErrors(prev => ({ ...prev, [key]: errorMessage }));
      setLoadingStates(prev => ({ ...prev, [key]: false }));
      throw error;
    }
  }, []);

  const setError = useCallback((key: string, error: string | null) => {
    setErrors(prev => ({ ...prev, [key]: error }));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const isLoading = useCallback((key: string) => {
    return loadingStates[key] || false;
  }, [loadingStates]);

  const getError = useCallback((key: string) => {
    return errors[key] || null;
  }, [errors]);

  const loading = Object.values(loadingStates).some(Boolean);

  return {
    loading,
    errors,
    execute,
    setError,
    clearErrors,
    isLoading,
    getError,
  };
}

// Hook for managing form submission states
interface UseFormStateOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  resetOnSuccess?: boolean;
}

interface UseFormStateReturn {
  loading: boolean;
  error: string | null;
  success: boolean;
  submit: (asyncFunction: () => Promise<any>) => Promise<void>;
  reset: () => void;
  setError: (error: string | null) => void;
}

export function useFormState(options: UseFormStateOptions = {}): UseFormStateReturn {
  const { onSuccess, onError, resetOnSuccess = true } = options;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const submit = useCallback(async (asyncFunction: () => Promise<any>) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await asyncFunction();
      setSuccess(true);
      onSuccess?.(result);
      
      if (resetOnSuccess) {
        setTimeout(() => {
          setSuccess(false);
        }, 3000); // Reset success state after 3 seconds
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || error?.message || 'An unexpected error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [onSuccess, onError, resetOnSuccess]);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setSuccess(false);
  }, []);

  const setErrorCallback = useCallback((error: string | null) => {
    setError(error);
  }, []);

  return {
    loading,
    error,
    success,
    submit,
    reset,
    setError: setErrorCallback,
  };
}

// Hook for managing pagination with async data
interface UsePaginatedAsyncStateOptions<T> {
  pageSize?: number;
  initialPage?: number;
  transform?: (data: any) => { items: T[]; total: number; hasMore: boolean };
}

interface UsePaginatedAsyncStateReturn<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  page: number;
  pageSize: number;
  total: number;
  hasMore: boolean;
  loadPage: (asyncFunction: (page: number, pageSize: number) => Promise<any>) => Promise<void>;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  setPage: (page: number) => void;
  reset: () => void;
}

export function usePaginatedAsyncState<T = any>(
  options: UsePaginatedAsyncStateOptions<T> = {}
): UsePaginatedAsyncStateReturn<T> {
  const { pageSize = 20, initialPage = 1, transform } = options;
  
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(initialPage);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [lastAsyncFunction, setLastAsyncFunction] = useState<((page: number, pageSize: number) => Promise<any>) | null>(null);

  const loadPage = useCallback(async (asyncFunction: (page: number, pageSize: number) => Promise<any>) => {
    setLoading(true);
    setError(null);
    setLastAsyncFunction(() => asyncFunction);

    try {
      const result = await asyncFunction(page, pageSize);
      
      let processedData;
      if (transform) {
        processedData = transform(result);
      } else {
        // Default transformation
        processedData = {
          items: result.data || result.items || result,
          total: result.total || result.count || 0,
          hasMore: result.hasMore !== undefined ? result.hasMore : (result.data?.length === pageSize)
        };
      }

      if (page === 1) {
        setData(processedData.items);
      } else {
        setData(prev => [...prev, ...processedData.items]);
      }
      
      setTotal(processedData.total);
      setHasMore(processedData.hasMore);
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || error?.message || 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, transform]);

  const loadMore = useCallback(async () => {
    if (hasMore && !loading && lastAsyncFunction) {
      setPage(prev => prev + 1);
    }
  }, [hasMore, loading, lastAsyncFunction]);

  const refresh = useCallback(async () => {
    if (lastAsyncFunction) {
      setPage(1);
      setData([]);
      await loadPage(lastAsyncFunction);
    }
  }, [lastAsyncFunction, loadPage]);

  const reset = useCallback(() => {
    setData([]);
    setLoading(false);
    setError(null);
    setPage(initialPage);
    setTotal(0);
    setHasMore(true);
    setLastAsyncFunction(null);
  }, [initialPage]);

  // Load data when page changes
  useEffect(() => {
    if (lastAsyncFunction && page > 1) {
      loadPage(lastAsyncFunction);
    }
  }, [page, lastAsyncFunction, loadPage]);

  return {
    data,
    loading,
    error,
    page,
    pageSize,
    total,
    hasMore,
    loadPage,
    loadMore,
    refresh,
    setPage,
    reset,
  };
}
