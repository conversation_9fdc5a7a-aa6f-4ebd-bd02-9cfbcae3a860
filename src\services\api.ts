import axios from 'axios';

const API_BASE_URL = 'http://localhost:8080/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface DispatcherCompany {
  _id: string;
  name: string;
  address?: string;
  phone?: string;
  contactEmail: string;
  createdAt: string;
  subscriptionPlan: 'basic' | 'pro' | 'enterprise';
  maxUsers: number;
  activeUsers: number;
  status: 'active' | 'inactive';
}

export interface DispatcherUser {
  _id: string;
  email: string;
  name: string;
  role: 'admin' | 'agent';
  dispatcherCompanyId: string;
  createdAt: string;
  status: 'active' | 'inactive';
}

export interface TruckingCompany {
  _id: string;
  name: string;
  mcNumber: string;
  dotNumber: string;
  address?: string;
  phone?: string;
  email?: string;
  contactPersonName?: string;
  contactPersonPhone?: string;
  contactPersonEmail?: string;
  insuranceProvider?: string;
  insuranceExpiryDate?: string;
  creditRating: 'excellent' | 'good' | 'fair' | 'poor' | 'not_rated';
  notes?: string;
  status: 'active' | 'inactive' | 'suspended';
  verified?: boolean;
  verificationNotes?: string;
  createdAt: string;
}

export interface Driver {
  _id: string;
  name: string;
  phone: string;
  email?: string;
  licenseNumber: string;
  licenseState?: string;
  licenseExpiryDate?: string;
  truckingCompanyId: string;
  truckType: 'dry_van' | 'refrigerated' | 'flatbed' | 'tanker' | 'car_carrier' | 'heavy_haul';
  truckMake?: string;
  truckModel?: string;
  truckYear?: number;
  trailerType?: string;
  maxWeight?: number;
  currentLocation?: string;
  homeBase?: string;
  available: boolean;
  status: 'active' | 'inactive' | 'suspended';
  notes?: string;
  createdAt: string;
}

export interface Load {
  _id: string;
  origin: string;
  destination: string;
  rate: number;
  perMileRate?: number;
  distanceInMiles: number;
  weightInPounds: number;
  equipment: string;
  trailerType?: string;
  loadSize?: string;
  pickupDate: string;
  deliveryDate: string;
  deliveryComments?: string;
  referenceId?: string;
  broker: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
    mcNumber?: string;
    usdotNumber?: string;
    creditRating?: string;
  };
  source: string;
  truckingCompanyId?: string;
  driverId?: string;
  assignedByUserId?: string;
  status: 'unassigned' | 'assigned' | 'in_transit' | 'delivered' | 'cancelled';
  createdAt: string;
}

export interface CallLog {
  id: string;
  loadId?: string;
  type: 'inboundPhoneCall' | 'outboundPhoneCall';
  startedAt: string;
  endedAt?: string;
  recordingUrl?: string;
  summary?: string;
  status: 'started' | 'ended' | 'failed';
  endedReason?: string;
  messages: Array<{
    role: 'user' | 'bot' | 'tool_calls' | 'tool_call_result';
    time: number;
    endTime?: number;
    message: string;
    duration?: number;
    secondsFromStart: number;
  }>;
  costBreakdown?: {
    stt: number;
    llm: number;
    tts: number;
    vapi: number;
    chat: number;
    total: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionPlan {
  key: string;
  name: string;
  maxUsers: number;
  maxDrivers: number;
  price: number;
  features: string[];
  isCurrent: boolean;
  isUpgrade: boolean;
  isDowngrade: boolean;
}

export interface SubscriptionUsage {
  users: {
    current: number;
    limit: number;
    percentage: number;
  };
  plan: SubscriptionPlan;
  billingPeriod: {
    start: string;
    end: string;
  };
}

// API Service Functions
export const apiService = {
  // Authentication
  login: async (email: string, password: string) => {
    return api.post('/auth/signin', { email, password });
  },

  signup: async (signupData: {
    companyName: string;
    adminName: string;
    adminEmail: string;
    adminPhone: string;
    password: string;
    subscriptionPlan?: string;
  }) => {
    return api.post('/auth/signup', {
      companyName: signupData.companyName,
      adminName: signupData.adminName,
      adminEmail: signupData.adminEmail,
      adminPhone: signupData.adminPhone,
      password: signupData.password,
      subscriptionPlan: signupData.subscriptionPlan || 'basic'
    });
  },

  // Dispatcher Company
  getCompany: async () => {
    return api.get('/api/dispatcher/company');
  },

  updateCompany: async (data: Partial<DispatcherCompany>) => {
    return api.put('/api/dispatcher/company', data);
  },

  // Team Management
  getTeamMembers: async () => {
    return api.get('/api/dispatcher/users');
  },

  inviteTeamMember: async (data: { name: string; email: string; role?: 'admin' | 'agent' }) => {
    return api.post('/api/dispatcher/users', data);
  },

  updateTeamMember: async (userId: string, data: Partial<DispatcherUser>) => {
    return api.put(`/api/dispatcher/users/${userId}`, data);
  },

  removeTeamMember: async (userId: string) => {
    return api.delete(`/api/dispatcher/users/${userId}`);
  },

  // Drivers
  getDrivers: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    truckingCompanyId?: string;
    available?: boolean;
  }) => {
    return api.get('/api/drivers', { params });
  },

  createDriver: async (data: Omit<Driver, '_id' | 'createdAt'>) => {
    return api.post('/api/drivers', data);
  },

  getDriver: async (id: string) => {
    return api.get(`/api/drivers/${id}`);
  },

  updateDriver: async (id: string, data: Partial<Driver>) => {
    return api.put(`/api/drivers/${id}`, data);
  },

  deleteDriver: async (id: string) => {
    return api.delete(`/api/drivers/${id}`);
  },

  updateDriverAvailability: async (id: string, data: { available: boolean; currentLocation?: string }) => {
    return api.patch(`/api/drivers/${id}/availability`, data);
  },

  // Trucking Companies
  getTruckingCompanies: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    verified?: boolean;
  }) => {
    return api.get('/api/trucking-companies', { params });
  },

  createTruckingCompany: async (data: Omit<TruckingCompany, '_id' | 'createdAt'>) => {
    return api.post('/api/trucking-companies', data);
  },

  getTruckingCompany: async (id: string) => {
    return api.get(`/api/trucking-companies/${id}`);
  },

  updateTruckingCompany: async (id: string, data: Partial<TruckingCompany>) => {
    return api.put(`/api/trucking-companies/${id}`, data);
  },

  deleteTruckingCompany: async (id: string) => {
    return api.delete(`/api/trucking-companies/${id}`);
  },

  verifyTruckingCompany: async (id: string, data: { verified: boolean; verificationNotes?: string }) => {
    return api.post(`/api/trucking-companies/${id}/verify`, data);
  },

  // Loads
  getLoads: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    truckingCompanyId?: string;
    driverId?: string;
    assignedByUserId?: string;
  }) => {
    return api.get('/api/loads', { params });
  },

  createLoad: async (data: Omit<Load, '_id' | 'createdAt'>) => {
    return api.post('/api/loads', data);
  },

  getLoad: async (id: string) => {
    return api.get(`/api/loads/${id}`);
  },

  updateLoad: async (id: string, data: Partial<Load>) => {
    return api.put(`/api/loads/${id}`, data);
  },

  deleteLoad: async (id: string) => {
    return api.delete(`/api/loads/${id}`);
  },

  assignLoad: async (loadId: string, data: { truckingCompanyId: string; driverId: string }) => {
    return api.post(`/api/loads/${loadId}/assign`, data);
  },

  unassignLoad: async (loadId: string) => {
    return api.post(`/api/loads/${loadId}/unassign`);
  },

  updateLoadStatus: async (loadId: string, status: 'unassigned' | 'assigned' | 'in_transit' | 'delivered' | 'cancelled') => {
    return api.patch(`/api/loads/${loadId}/status`, { status });
  },

  triggerAICall: async (loadId: string, driverId: string) => {
    return api.post(`/api/loads/${loadId}/call`, { driverId });
  },

  // Legacy load endpoints (for backward compatibility)
  getAllLoads: async () => {
    return api.post('/scrape');
  },

  getCurrentLoad: async () => {
    return api.post('/current');
  },

  getLoadById: async (loadId: string) => {
    return api.get(`/${loadId}`);
  },

  getLoadsByUser: async (userId: string) => {
    return api.get(`/user/${userId}`);
  },

  getCurrentLoadsByUser: async (userId: string) => {
    return api.get(`/current/user/${userId}`);
  },

  getCurrentLoadById: async (loadId: string) => {
    return api.get(`/current/${loadId}`);
  },

  // Call Logs
  getCallLogs: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    type?: string;
    status?: string;
    loadId?: string;
    startDate?: string;
    endDate?: string;
  }) => {
    return api.get('/api/call-logs', { params });
  },

  saveCallLog: async (data: Partial<CallLog>) => {
    return api.post('/call-logs', data);
  },

  getCallLog: async (id: string) => {
    return api.get(`/call-logs/${id}`);
  },

  getCallLogsByUser: async (userId: string) => {
    return api.get(`/call-logs/user/${userId}`);
  },

  deleteCallLog: async (id: string) => {
    return api.delete(`/api/call-logs/${id}`);
  },

  exportCallLogs: async (params?: {
    startDate?: string;
    endDate?: string;
    format?: 'csv' | 'json';
  }) => {
    return api.get('/api/call-logs/export', { params });
  },

  // Subscription
  getCurrentSubscription: async () => {
    return api.get('/api/subscription/current');
  },

  getSubscriptionPlans: async () => {
    return api.get('/api/subscription/plans');
  },

  upgradeSubscription: async (newPlan: string) => {
    return api.post('/api/subscription/upgrade', { newPlan });
  },

  downgradeSubscription: async (newPlan: string) => {
    return api.post('/api/subscription/downgrade', { newPlan });
  },

  getSubscriptionUsage: async () => {
    return api.get('/api/subscription/usage');
  },
};

export default api;
