import React, { ReactNode } from 'react';

interface CardDataStatsProps {
  title: string;
  total: string;
  rate: string;
  levelUp?: boolean;
  levelDown?: boolean;
  children: ReactNode;
  subtitle?: string;
  iconBgColor?: 'blue' | 'orange' | 'green' | 'gray';
}

const CardDataStats: React.FC<CardDataStatsProps> = ({
  title,
  total,
  rate,
  levelUp,
  levelDown,
  children,
  subtitle,
  iconBgColor = 'blue',
}) => {
  const getIconBgClass = () => {
    switch (iconBgColor) {
      case 'blue':
        return 'bg-primary-blue/10 text-primary-blue';
      case 'orange':
        return 'bg-secondary-orange/10 text-secondary-orange';
      case 'green':
        return 'bg-accent-green/10 text-accent-green';
      case 'gray':
        return 'bg-neutral-gray/10 text-neutral-gray';
      default:
        return 'bg-primary-blue/10 text-primary-blue';
    }
  };

  const getTrendColor = () => {
    if (levelUp) return 'text-accent-green';
    if (levelDown) return 'text-secondary-orange';
    return 'text-neutral-gray';
  };

  return (
    <div className="professional-metric-card group">
      {/* Icon Section */}
      <div className={`flex h-14 w-14 items-center justify-center rounded-xl ${getIconBgClass()} transition-all duration-200 group-hover:scale-110`}>
        {children}
      </div>

      {/* Content Section */}
      <div className="mt-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-3xl font-bold text-text-primary mb-1 tracking-tight">
              {total}
            </h3>
            <p className="text-lg font-semibold text-text-primary mb-1">
              {title}
            </p>
            {subtitle && (
              <p className="text-sm text-text-secondary">
                {subtitle}
              </p>
            )}
          </div>

          {/* Trend Indicator */}
          <div className={`flex items-center gap-1.5 text-sm font-semibold ${getTrendColor()}`}>
            {levelUp && (
              <div className="flex items-center gap-1 bg-accent-green/10 px-2 py-1 rounded-full">
                <svg
                  className="fill-current w-3 h-3"
                  viewBox="0 0 10 11"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4.35716 2.47737L0.908974 5.82987L5.0443e-07 4.94612L5 0.0848689L10 4.94612L9.09103 5.82987L5.64284 2.47737L5.64284 10.0849L4.35716 10.0849L4.35716 2.47737Z"
                    fill=""
                  />
                </svg>
                <span>{rate}</span>
              </div>
            )}
            {levelDown && (
              <div className="flex items-center gap-1 bg-secondary-orange/10 px-2 py-1 rounded-full">
                <svg
                  className="fill-current w-3 h-3"
                  viewBox="0 0 10 11"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5.64284 7.69237L9.09102 4.33987L10 5.22362L5 10.0849L-8.98488e-07 5.22362L0.908973 4.33987L4.35716 7.69237L4.35716 0.0848701L5.64284 0.0848704L5.64284 7.69237Z"
                    fill=""
                  />
                </svg>
                <span>{rate}</span>
              </div>
            )}
            {!levelUp && !levelDown && (
              <div className="flex items-center gap-1 bg-neutral-gray/10 px-2 py-1 rounded-full">
                <span>{rate}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardDataStats;
