import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { loadGroupsAPI, LoadGroup } from '../services/api';

const LoadGroups: React.FC = () => {
  const [loadGroups, setLoadGroups] = useState<LoadGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchLoadGroups();
  }, [currentPage]);

  const fetchLoadGroups = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await loadGroupsAPI.getLoadGroups(currentPage, 10);
      setLoadGroups(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err: any) {
      console.error('Error fetching load groups:', err);
      setError(err.response?.data?.message || 'Failed to fetch load groups');
    } finally {
      setLoading(false);
    }
  };

  const handleCardClick = (loadGroupId: string) => {
    navigate(`/load-groups/${loadGroupId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const filteredLoadGroups = loadGroups.filter(group =>
    !searchTerm ||
    group.loadGroupId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.assignedByDispatcherUserId.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.dispatcherCompanyId.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.loads.some(load => 
      load.pickupLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
      load.deliveryLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
      load.broker.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-background-primary p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background-primary p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <div className="text-red-600 text-lg font-medium mb-2">Error Loading Load Groups</div>
            <div className="text-red-500 mb-4">{error}</div>
            <button
              onClick={fetchLoadGroups}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background-primary p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text-primary mb-2">Load Groups</h1>
          <p className="text-text-secondary">
            Manage and view load groups scraped from DAT and assigned to drivers
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-border-color p-6 mb-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search by group ID, dispatcher, company, locations, or broker..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>
          </div>
        </div>

        {/* Load Groups Grid */}
        {filteredLoadGroups.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-border-color p-12 text-center">
            <div className="text-text-secondary text-lg mb-2">No Load Groups Found</div>
            <div className="text-text-secondary">
              {searchTerm ? 'Try adjusting your search criteria.' : 'No load groups have been created yet.'}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {filteredLoadGroups.map((group) => {
              const totalRate = group.loads.reduce((sum, load) => sum + load.rate, 0);
              const avgPriorityScore = group.loads.reduce((sum, load) => sum + load.ai.priorityScore, 0) / group.loads.length;
              const primaryLoad = group.loads[0];

              return (
                <div
                  key={group._id}
                  onClick={() => handleCardClick(group._id)}
                  className="bg-white rounded-lg shadow-sm border border-border-color hover:shadow-md transition-all duration-200 cursor-pointer group"
                >
                  {/* Card Header */}
                  <div className="p-6 border-b border-border-color">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-text-primary group-hover:text-primary-blue transition-colors">
                          {group.loadGroupId}
                        </h3>
                        <p className="text-sm text-text-secondary">
                          {group.loads.length} load{group.loads.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {group.aiPrioritized && (
                          <span className="bg-accent-green bg-opacity-10 text-accent-green px-2 py-1 rounded-full text-xs font-medium">
                            AI Priority
                          </span>
                        )}
                        <span className="bg-primary-blue bg-opacity-10 text-primary-blue px-2 py-1 rounded-full text-xs font-medium">
                          {group.source}
                        </span>
                      </div>
                    </div>
                    
                    {/* Primary Route */}
                    <div className="flex items-center gap-2 text-sm text-text-secondary mb-2">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="font-medium">{primaryLoad.pickupLocation}</span>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      <span className="font-medium">{primaryLoad.deliveryLocation}</span>
                    </div>

                    {/* Total Rate */}
                    <div className="text-2xl font-bold text-accent-green">
                      {formatCurrency(totalRate)}
                    </div>
                  </div>

                  {/* Card Body */}
                  <div className="p-6">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <div className="text-xs text-text-secondary mb-1">Avg Priority Score</div>
                        <div className="text-sm font-medium text-text-primary">
                          {Math.round(avgPriorityScore)}/100
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-text-secondary mb-1">Total Distance</div>
                        <div className="text-sm font-medium text-text-primary">
                          {group.loads.reduce((sum, load) => sum + load.distanceInMiles, 0).toLocaleString()} mi
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div>
                        <div className="text-xs text-text-secondary">Assigned By</div>
                        <div className="text-sm font-medium text-text-primary">
                          {group.assignedByDispatcherUserId.name}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-text-secondary">Company</div>
                        <div className="text-sm font-medium text-text-primary">
                          {group.dispatcherCompanyId.name}
                        </div>
                      </div>
                    </div>

                    <div className="text-xs text-text-secondary">
                      Scraped: {formatDate(group.metadata.scrapedAt)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center gap-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 border border-border-color rounded-lg text-text-secondary hover:bg-background-light disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Previous
            </button>
            
            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    currentPage === page
                      ? 'bg-primary-blue text-white'
                      : 'text-text-secondary hover:bg-background-light'
                  }`}
                >
                  {page}
                </button>
              ))}
            </div>

            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-4 py-2 border border-border-color rounded-lg text-text-secondary hover:bg-background-light disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadGroups;
