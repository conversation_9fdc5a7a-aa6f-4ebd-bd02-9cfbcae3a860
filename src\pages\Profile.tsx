import { useSelector } from 'react-redux';
import Breadcrumb from '../components/Breadcrumbs/Breadcrumb';
import { RootState } from '../store/store';

const Profile = () => {
  const user = useSelector((state: RootState) => state.auth.user);

  return (
    <>
      <Breadcrumb pageName="Profile" />

      <div className="overflow-hidden rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div className="relative z-20 h-35 md:h-65">
          <div


            className="h-full w-full rounded-tl-sm rounded-tr-sm object-cover object-center bg-black"
          />

        </div>
        <div className="px-4 pb-6 text-center lg:pb-8 xl:pb-11.5">
          <div className="relative z-30 mx-auto -mt-22 h-30 w-full max-w-30 rounded-full bg-white/20 p-1 backdrop-blur sm:h-44 sm:max-w-44 sm:p-3">
            <div className="relative drop-shadow-2">
              <img src={user?.profile_image} alt="profile"  className='rounded-full'/>

            </div>
          </div>
          <div className="mt-4">
            <h3 className="mb-1.5 text-2xl font-semibold text-black ">
              {user?.name}
            </h3>

            <h3 className="mb-1.5 text-2xl font-semibold text-black ">
              {user?.email}
            </h3>



          </div>
        </div>
      </div>
    </>
  );
};

export default Profile;
