import React from 'react';
import CardDataStats from '../../components/CardDataStats';
import ChartOne from '../../components/Charts/ChartOne';
import ChartThree from '../../components/Charts/ChartThree';
import ChartTwo from '../../components/Charts/ChartTwo';

const ECommerce: React.FC = () => {
  return (
    <>
      {/* Professional Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-text-primary mb-2">
          Truck Dispatching Dashboard
        </h1>
        <p className="text-text-secondary">
          Monitor your fleet performance, load management, and operational metrics in real-time
        </p>
      </div>

      {/* Professional Metrics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4 mb-8">
        <CardDataStats
          title="Active Loads"
          total="24"
          rate="8.2%"
          levelUp
          subtitle="Currently in transit"
          iconBgColor="blue"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M19 7h-3V6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h1.05A3.5 3.5 0 0 0 9.5 16.5a3.5 3.5 0 0 0 3.45-2.5h1.1A3.5 3.5 0 0 0 17.5 16.5a3.5 3.5 0 0 0 3.45-2.5H22v-4a3 3 0 0 0-3-3zM9.5 18a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zm8 0a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zM20 12h-3V9h2a1 1 0 0 1 1 1z"/>
          </svg>
        </CardDataStats>

        <CardDataStats
          title="Total Revenue"
          total="$127.5K"
          rate="12.4%"
          levelUp
          subtitle="This month"
          iconBgColor="green"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"/>
          </svg>
        </CardDataStats>

        <CardDataStats
          title="Fleet Utilization"
          total="87%"
          rate="3.1%"
          levelUp
          subtitle="Trucks in operation"
          iconBgColor="orange"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10zM7 9a1 1 0 1 1-1 1 1 1 0 0 1 1-1zm5 7H9.41l.64-.64a1 1 0 0 0 0-1.41 1 1 0 0 0-1.41 0l-2.64 2.64a1 1 0 0 0 0 1.41L8.64 20a1 1 0 0 0 1.41 0 1 1 0 0 0 0-1.41L9.41 18H12a1 1 0 0 0 0-2z"/>
          </svg>
        </CardDataStats>

        <CardDataStats
          title="Driver Performance"
          total="94.2%"
          rate="1.8%"
          levelDown
          subtitle="Average rating"
          iconBgColor="gray"
        >
          <svg
            className="fill-current w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </CardDataStats>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-12 gap-6">
        <ChartOne />
        <ChartTwo />
        <ChartThree />
      </div>
    </>
  );
};

export default ECommerce;
