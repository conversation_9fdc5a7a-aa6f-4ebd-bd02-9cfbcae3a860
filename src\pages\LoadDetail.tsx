import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { loadGroupsAPI, LoadGroup, LoadGroupLoad } from '../services/api';

const LoadDetail: React.FC = () => {
  const { groupId, loadId } = useParams<{ groupId: string; loadId: string }>();
  const navigate = useNavigate();
  const [loadGroup, setLoadGroup] = useState<LoadGroup | null>(null);
  const [load, setLoad] = useState<LoadGroupLoad | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (groupId && loadId) {
      fetchLoadDetails();
    }
  }, [groupId, loadId]);

  const fetchLoadDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await loadGroupsAPI.getLoadGroupById(groupId!);
      setLoadGroup(response.data);
      
      // Find the specific load within the group
      const foundLoad = response.data.loads.find(l => l._id === loadId);
      if (foundLoad) {
        setLoad(foundLoad);
      } else {
        setError('Load not found in this group');
      }
    } catch (err: any) {
      console.error('Error fetching load details:', err);
      setError(err.response?.data?.message || 'Failed to fetch load details');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background-primary p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !load || !loadGroup) {
    return (
      <div className="min-h-screen bg-background-primary p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <div className="text-red-600 text-lg font-medium mb-2">Error Loading Load Details</div>
            <div className="text-red-500 mb-4">{error || 'Load not found'}</div>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => navigate(`/load-groups/${groupId}`)}
                className="bg-neutral-gray-medium text-white px-4 py-2 rounded-lg hover:bg-neutral-gray-dark transition-colors"
              >
                Back to Load Group
              </button>
              <button
                onClick={fetchLoadDetails}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const loadIndex = loadGroup.loads.findIndex(l => l._id === loadId) + 1;

  return (
    <div className="min-h-screen bg-background-primary p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => navigate(`/load-groups/${groupId}`)}
              className="flex items-center gap-2 text-text-secondary hover:text-primary-blue transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Load Group
            </button>
          </div>
          
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold text-text-primary mb-2">
                Load #{loadIndex} - {load.referenceId}
              </h1>
              <p className="text-text-secondary">
                From group {loadGroup.loadGroupId} • {loadGroup.source}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {load.ai.call.callTriggered && (
                <span className="bg-accent-green bg-opacity-10 text-accent-green px-3 py-1 rounded-full text-sm font-medium">
                  AI Call Made
                </span>
              )}
              <span className="bg-secondary-orange bg-opacity-10 text-secondary-orange px-3 py-1 rounded-full text-sm font-medium">
                Priority: {load.ai.priorityScore}/100
              </span>
            </div>
          </div>
        </div>

        {/* Main Route Information */}
        <div className="bg-white rounded-lg shadow-sm border border-border-color p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-lg text-text-primary">
                <svg className="w-6 h-6 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="font-semibold">{load.pickupLocation}</span>
              </div>
              <svg className="w-8 h-8 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <div className="flex items-center gap-2 text-lg text-text-primary">
                <svg className="w-6 h-6 text-secondary-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="font-semibold">{load.deliveryLocation}</span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-accent-green">{formatCurrency(load.rate)}</div>
              <div className="text-text-secondary">${load.perMileRate}/mile</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="text-sm text-text-secondary mb-1">Pickup Date</div>
              <div className="text-lg font-medium text-text-primary">{formatDate(load.pickupDate)}</div>
            </div>
            
            <div>
              <div className="text-sm text-text-secondary mb-1">Delivery Date</div>
              <div className="text-lg font-medium text-text-primary">{formatDate(load.deliveryDate)}</div>
            </div>
          </div>
        </div>

        {/* Load Specifications */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-border-color p-6">
            <h2 className="text-xl font-semibold text-text-primary mb-4">Load Specifications</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-text-secondary mb-1">Distance</div>
                  <div className="text-lg font-medium text-text-primary">{load.distanceInMiles.toLocaleString()} miles</div>
                </div>
                
                <div>
                  <div className="text-sm text-text-secondary mb-1">Weight</div>
                  <div className="text-lg font-medium text-text-primary">{load.weightInPounds.toLocaleString()} lbs</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-text-secondary mb-1">Equipment</div>
                  <div className="text-lg font-medium text-text-primary">{load.equipment}</div>
                </div>
                
                <div>
                  <div className="text-sm text-text-secondary mb-1">Trailer Type</div>
                  <div className="text-lg font-medium text-text-primary">{load.trailerType}</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-text-secondary mb-1">Load Size</div>
                  <div className="text-lg font-medium text-text-primary">{load.loadSize}</div>
                </div>
                
                <div>
                  <div className="text-sm text-text-secondary mb-1">Deadhead</div>
                  <div className="text-lg font-medium text-text-primary">
                    Pickup: {load.deadheadPickup}mi • Delivery: {load.deadheadDelivery}mi
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* AI Information */}
          <div className="bg-white rounded-lg shadow-sm border border-border-color p-6">
            <h2 className="text-xl font-semibold text-text-primary mb-4">AI Analysis</h2>
            <div className="space-y-4">
              <div>
                <div className="text-sm text-text-secondary mb-1">Priority Score</div>
                <div className="flex items-center gap-3">
                  <div className="text-2xl font-bold text-secondary-orange">{load.ai.priorityScore}/100</div>
                  <div className="flex-1 bg-background-light rounded-full h-3">
                    <div 
                      className="bg-secondary-orange h-3 rounded-full transition-all duration-300"
                      style={{ width: `${load.ai.priorityScore}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              
              {load.ai.call.callTriggered ? (
                <div className="bg-accent-green bg-opacity-10 rounded-lg p-4">
                  <h3 className="text-sm font-semibold text-accent-green mb-3">AI Call Information</h3>
                  <div className="space-y-2">
                    <div>
                      <div className="text-xs text-text-secondary">Platform</div>
                      <div className="text-sm font-medium text-text-primary">{load.ai.call.callPlatform}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-text-secondary">Call ID</div>
                      <div className="text-sm font-medium text-text-primary">{load.ai.call.callId}</div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-accent-green font-medium">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Call Successfully Triggered
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-neutral-gray-light bg-opacity-50 rounded-lg p-4">
                  <div className="text-sm text-text-secondary">No AI call has been triggered for this load</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Broker Information */}
        <div className="bg-white rounded-lg shadow-sm border border-border-color p-6 mb-8">
          <h2 className="text-xl font-semibold text-text-primary mb-4">Broker Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <div className="text-sm text-text-secondary mb-1">Company Name</div>
              <div className="text-lg font-medium text-text-primary">{load.broker.name}</div>
            </div>
            
            <div>
              <div className="text-sm text-text-secondary mb-1">Phone</div>
              <div className="text-lg font-medium text-text-primary">
                <a href={`tel:${load.broker.phone}`} className="text-primary-blue hover:underline">
                  {load.broker.phone}
                </a>
              </div>
            </div>
            
            <div>
              <div className="text-sm text-text-secondary mb-1">Email</div>
              <div className="text-lg font-medium text-text-primary">
                <a href={`mailto:${load.broker.email}`} className="text-primary-blue hover:underline">
                  {load.broker.email}
                </a>
              </div>
            </div>
            
            <div>
              <div className="text-sm text-text-secondary mb-1">MC Number</div>
              <div className="text-lg font-medium text-text-primary">{load.broker.mcNumber}</div>
            </div>
            
            <div>
              <div className="text-sm text-text-secondary mb-1">DOT Number</div>
              <div className="text-lg font-medium text-text-primary">{load.broker.usdotNumber}</div>
            </div>
            
            <div>
              <div className="text-sm text-text-secondary mb-1">Credit Rating</div>
              <div className="text-lg font-medium text-text-primary">
                <span className={`px-2 py-1 rounded-full text-sm font-medium ${
                  load.broker.creditRating === 'A' ? 'bg-accent-green bg-opacity-10 text-accent-green' :
                  load.broker.creditRating === 'B' ? 'bg-secondary-orange bg-opacity-10 text-secondary-orange' :
                  'bg-red-100 text-red-600'
                }`}>
                  {load.broker.creditRating}
                </span>
              </div>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="text-sm text-text-secondary mb-1">Address</div>
            <div className="text-lg font-medium text-text-primary">{load.broker.address}</div>
          </div>
        </div>

        {/* Delivery Comments */}
        {load.deliveryComments && (
          <div className="bg-white rounded-lg shadow-sm border border-border-color p-6">
            <h2 className="text-xl font-semibold text-text-primary mb-4">Delivery Comments</h2>
            <div className="bg-background-light rounded-lg p-4">
              <div className="text-text-primary">{load.deliveryComments}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadDetail;
