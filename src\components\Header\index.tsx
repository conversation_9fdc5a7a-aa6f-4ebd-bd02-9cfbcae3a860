import { Link } from 'react-router-dom';
import DropdownUser from './DropdownUser';

const Header = () => {
  return (
    <header className="sticky top-0 z-999 flex w-full bg-white border-b border-border-color shadow-sm">
      <div className="flex flex-grow items-center justify-between px-4 py-4 md:px-6 2xl:px-8">

        {/* Desktop Search and Status */}
        <div className="hidden lg:flex items-center gap-6">
          <div className="flex items-center gap-4">
            {/* Status Indicator */}
            <div className="flex items-center gap-2 px-3 py-1.5 bg-accent-green/10 rounded-full">
              <div className="w-2 h-2 bg-accent-green rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-accent-green">System Online</span>
            </div>

            {/* Quick Stats */}
            <div className="flex items-center gap-4 text-sm text-text-secondary">
              <div className="flex items-center gap-1">
                <svg className="w-4 h-4 text-primary-blue" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
                <span className="font-medium text-text-primary">24</span>
                <span>Active Loads</span>
              </div>
              <div className="w-px h-4 bg-border-color"></div>
              <div className="flex items-center gap-1">
                <svg className="w-4 h-4 text-secondary-orange" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                </svg>
                <span className="font-medium text-text-primary">87%</span>
                <span>Fleet Utilization</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side Actions */}
        <div className="flex items-center gap-4">
          {/* Notifications */}
          <button className="relative flex h-10 w-10 items-center justify-center rounded-lg border border-border-color bg-white hover:bg-background-light transition-colors duration-200">
            <svg className="w-5 h-5 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z" />
            </svg>
            <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-secondary-orange text-xs font-medium text-white">
              3
            </span>
          </button>

          {/* User Dropdown */}
          <DropdownUser />
        </div>
      </div>
    </header>
  );
};

export default Header;
