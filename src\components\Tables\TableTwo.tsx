import axios from 'axios';
import { useEffect, useState } from 'react';
import { BASE_URL } from '../../lib/constants';

interface User {
  id: string;
  email: string;
  name: string;
  profile_image: string;
}

// Represents information about the company related to the load
interface CompanyInformation {
  address: string;          // Company's address
  mcNumber: string;         // Motor Carrier number
  usdotNumber: string;      // USDOT number
  call: string;             // Contact phone number
}

// Represents the delivery instructions for the load
interface DeliveryInstructions {
  deliveryInstructions: string; // Detailed delivery instructions
}

// Represents details about the load itself
interface LoadsDetails {
  location: string;               // Pickup location
  destination: string;            // Delivery destination
  dateAndTime: string;            // Date and time for pickup/delivery
  drivingTime: string;            // Estimated driving time for the load
  trailerType: string;            // Type of trailer required for the load
  loadSize: string;               // Size category of the load (e.g., TL, LTL)
  weight: string;                 // Weight of the load
  postedRate: string;             // Rate posted for the load
  marketRateEstimate: string;     // Estimated market rate for comparison
  pricePerMile: string;     // Price per mile for the load
  price: string;            // Total price for the load
  deadhead: string;         // Distance without a load (deadhead miles)
  company: string;           // Name of the company
  mileage: string;          // Total mileage for the load
}

// Represents a load object containing various details
interface Load {
  _id: string;                     // Unique identifier for the load
  companyInformation: CompanyInformation; // Company information related to the load
  deliveryInstructions: DeliveryInstructions; // Instructions for delivery
  loadsDetails: LoadsDetails;      // Specific details about the load
  userId: string;                  // ID of the user associated with the load
  platformName: string;            // URL to the load on the platform
  id: string;                      // Another identifier (could be for different purposes)
  createdAt: string;               // Timestamp of when the load was created
  updatedAt: string;               // Timestamp of the last update to the load
  __v: number;                     // Version key (typically used by MongoDB)
}


const TableTwo = () => {
  const [loads, setLoads] = useState<Load[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    if (!storedUser) {
      console.error('User not found in localStorage');
      return;
    }

    const user: User = JSON.parse(storedUser);
    const userId = user?.id;

    if (!userId) {
      console.error('User ID not found');
      return;
    }

    const fetchLoads = async () => {
      try {
        const response = await axios.get(`${BASE_URL}/loads/current/user/${userId}`);
        console.log(response.data); // Log the response for debugging
        setLoads(Array.isArray(response.data.currentLoads) ? response.data.currentLoads : []);
      } catch (err: any) {
        setError(`Failed to fetch loads: ${err?.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchLoads();
  }, []);

  if (loading) return <div className="flex justify-center items-center h-64"><div className="spinner">Loading...</div></div>;
  if (error) return <div>Error: {error}</div>;

  const formatCurrency = (value: string) => {
    if (!value) return 'N/A';
    // Remove any existing currency symbols and format
    const numericValue = value.replace(/[^0-9.]/g, '');
    return numericValue ? `$${parseFloat(numericValue).toLocaleString()}` : 'N/A';
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-text-primary mb-1">Available Loads</h2>
          <p className="text-text-secondary">Manage and track your freight loads</p>
        </div>
        <div className="flex items-center gap-3">
          <span className="text-sm text-text-secondary">
            Total: <span className="font-semibold text-text-primary">{loads.length}</span> loads
          </span>
        </div>
      </div>

      {Array.isArray(loads) && loads.length > 0 ? (
        <div className="grid gap-6">
          {loads.map((load) => (
            <div key={load._id} className="professional-card group hover:shadow-lg transition-all duration-300">
              {/* Load Header */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-primary-blue/10 text-primary-blue">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 7h-3V6a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h1.05A3.5 3.5 0 0 0 9.5 16.5a3.5 3.5 0 0 0 3.45-2.5h1.1A3.5 3.5 0 0 0 17.5 16.5a3.5 3.5 0 0 0 3.45-2.5H22v-4a3 3 0 0 0-3-3zM9.5 18a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zm8 0a1.5 1.5 0 1 1 1.5-1.5 1.5 1.5 0 0 1-1.5 1.5zM20 12h-3V9h2a1 1 0 0 1 1 1z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-text-primary mb-1">
                      {load.loadsDetails.company}
                    </h3>
                    <p className="text-text-secondary">
                      Load ID: {load._id.slice(-8).toUpperCase()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-accent-green mb-1">
                    {formatCurrency(load.loadsDetails.price)}
                  </div>
                  <div className="text-sm text-text-secondary">
                    {formatCurrency(load.loadsDetails.pricePerMile)}/mile
                  </div>
                </div>
              </div>

              {/* Route Information */}
              <div className="bg-background-light rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-3 h-3 rounded-full bg-primary-blue"></div>
                      <span className="font-semibold text-text-primary">Pickup</span>
                    </div>
                    <p className="text-text-secondary text-sm">{load.loadsDetails.location}</p>
                  </div>

                  <div className="flex items-center gap-2 px-4">
                    <div className="flex-1 h-0.5 bg-border-color"></div>
                    <svg className="w-5 h-5 text-neutral-gray" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    <div className="flex-1 h-0.5 bg-border-color"></div>
                  </div>

                  <div className="flex-1 text-right">
                    <div className="flex items-center justify-end gap-2 mb-2">
                      <span className="font-semibold text-text-primary">Delivery</span>
                      <div className="w-3 h-3 rounded-full bg-accent-green"></div>
                    </div>
                    <p className="text-text-secondary text-sm">{load.loadsDetails.destination}</p>
                  </div>
                </div>
              </div>

              {/* Load Details Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-white border border-border-color rounded-lg p-3">
                  <div className="text-sm text-text-secondary mb-1">Distance</div>
                  <div className="font-semibold text-text-primary">{load.loadsDetails.mileage} mi</div>
                </div>
                <div className="bg-white border border-border-color rounded-lg p-3">
                  <div className="text-sm text-text-secondary mb-1">Weight</div>
                  <div className="font-semibold text-text-primary">{load.loadsDetails.weight}</div>
                </div>
                <div className="bg-white border border-border-color rounded-lg p-3">
                  <div className="text-sm text-text-secondary mb-1">Trailer Type</div>
                  <div className="font-semibold text-text-primary">{load.loadsDetails.trailerType}</div>
                </div>
                <div className="bg-white border border-border-color rounded-lg p-3">
                  <div className="text-sm text-text-secondary mb-1">Deadhead</div>
                  <div className="font-semibold text-text-primary">{load.loadsDetails.deadhead}</div>
                </div>
              </div>

              {/* Company Information */}
              <div className="border-t border-border-color pt-4">
                <h4 className="font-semibold text-text-primary mb-3">Company Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-text-secondary mb-1">MC Number</div>
                    <div className="font-medium text-text-primary">{load.companyInformation.mcNumber}</div>
                  </div>
                  <div>
                    <div className="text-sm text-text-secondary mb-1">USDOT Number</div>
                    <div className="font-medium text-text-primary">{load.companyInformation.usdotNumber}</div>
                  </div>
                </div>
              </div>

              {/* Delivery Instructions */}
              {load.deliveryInstructions.deliveryInstructions && (
                <div className="border-t border-border-color pt-4 mt-4">
                  <h4 className="font-semibold text-text-primary mb-2">Delivery Instructions</h4>
                  <p className="text-text-secondary text-sm leading-relaxed">
                    {load.deliveryInstructions.deliveryInstructions}
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="border-t border-border-color pt-4 mt-4 flex gap-3">
                <button className="truck-btn-primary flex-1">
                  Accept Load
                </button>
                <button className="truck-btn-secondary">
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="professional-card text-center py-12">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 bg-neutral-gray/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-neutral-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-text-primary mb-2">No loads available</h3>
              <p className="text-text-secondary">New freight loads will appear here when they become available</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TableTwo;
