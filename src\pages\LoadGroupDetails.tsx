import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { loadGroupsAPI, LoadGroup } from '../services/api';

const LoadGroupDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loadGroup, setLoadGroup] = useState<LoadGroup | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (id) {
      fetchLoadGroupDetails();
    }
  }, [id]);

  const fetchLoadGroupDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await loadGroupsAPI.getLoadGroupById(id!);
      setLoadGroup(response.data);
    } catch (err: any) {
      console.error('Error fetching load group details:', err);
      setError(err.response?.data?.message || 'Failed to fetch load group details');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleLoadClick = (loadId: string) => {
    navigate(`/load-groups/${id}/loads/${loadId}`);
  };

  const filteredLoads = loadGroup?.loads.filter(load =>
    !searchTerm ||
    load.referenceId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    load.pickupLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
    load.deliveryLocation.toLowerCase().includes(searchTerm.toLowerCase()) ||
    load.broker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    load.equipment.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  if (loading) {
    return (
      <div className="min-h-screen bg-background-primary p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !loadGroup) {
    return (
      <div className="min-h-screen bg-background-primary p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <div className="text-red-600 text-lg font-medium mb-2">Error Loading Load Group</div>
            <div className="text-red-500 mb-4">{error || 'Load group not found'}</div>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => navigate('/load-groups')}
                className="bg-neutral-gray-medium text-white px-4 py-2 rounded-lg hover:bg-neutral-gray-dark transition-colors"
              >
                Back to Load Groups
              </button>
              <button
                onClick={fetchLoadGroupDetails}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const totalRate = loadGroup.loads.reduce((sum, load) => sum + load.rate, 0);
  const totalDistance = loadGroup.loads.reduce((sum, load) => sum + load.distanceInMiles, 0);
  const totalWeight = loadGroup.loads.reduce((sum, load) => sum + load.weightInPounds, 0);
  const avgPriorityScore = loadGroup.loads.reduce((sum, load) => sum + load.ai.priorityScore, 0) / loadGroup.loads.length;

  return (
    <div className="min-h-screen bg-background-primary p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => navigate('/load-groups')}
              className="flex items-center gap-2 text-text-secondary hover:text-primary-blue transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Load Groups
            </button>
          </div>

          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold text-text-primary mb-2">{loadGroup.loadGroupId}</h1>
              <p className="text-text-secondary">
                {loadGroup.loads.length} load{loadGroup.loads.length !== 1 ? 's' : ''} •
                Scraped from {loadGroup.source} •
                {formatDate(loadGroup.metadata.scrapedAt)}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {loadGroup.aiPrioritized && (
                <span className="bg-accent-green bg-opacity-10 text-accent-green px-3 py-1 rounded-full text-sm font-medium">
                  AI Prioritized
                </span>
              )}
              <span className="bg-primary-blue bg-opacity-10 text-primary-blue px-3 py-1 rounded-full text-sm font-medium">
                {loadGroup.source}
              </span>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-border-color p-6">
            <div className="text-sm text-text-secondary mb-1">Total Rate</div>
            <div className="text-2xl font-bold text-accent-green">{formatCurrency(totalRate)}</div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-border-color p-6">
            <div className="text-sm text-text-secondary mb-1">Total Distance</div>
            <div className="text-2xl font-bold text-text-primary">{totalDistance.toLocaleString()} mi</div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-border-color p-6">
            <div className="text-sm text-text-secondary mb-1">Total Weight</div>
            <div className="text-2xl font-bold text-text-primary">{(totalWeight / 1000).toFixed(1)}k lbs</div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-border-color p-6">
            <div className="text-sm text-text-secondary mb-1">Avg Priority Score</div>
            <div className="text-2xl font-bold text-secondary-orange">{Math.round(avgPriorityScore)}/100</div>
          </div>
        </div>

        {/* Assignment Information */}
        <div className="bg-white rounded-lg shadow-sm border border-border-color p-6 mb-8">
          <h2 className="text-xl font-semibold text-text-primary mb-4">Assignment Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <div className="text-sm text-text-secondary mb-1">Assigned By</div>
              <div className="text-lg font-medium text-text-primary">{loadGroup.assignedByDispatcherUserId.name}</div>
              <div className="text-sm text-text-secondary">{loadGroup.assignedByDispatcherUserId.email}</div>
            </div>

            <div>
              <div className="text-sm text-text-secondary mb-1">Company</div>
              <div className="text-lg font-medium text-text-primary">{loadGroup.dispatcherCompanyId.name}</div>
            </div>

            <div>
              <div className="text-sm text-text-secondary mb-1">Driver ID</div>
              <div className="text-lg font-medium text-text-primary">{loadGroup.driverId}</div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-border-color p-6 mb-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search loads by reference ID, locations, broker, or equipment..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>
          </div>
        </div>

        {/* Individual Load Cards */}
        {filteredLoads.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-border-color p-12 text-center">
            <div className="text-text-secondary text-lg mb-2">No Loads Found</div>
            <div className="text-text-secondary">
              {searchTerm ? 'Try adjusting your search criteria.' : 'No loads available in this group.'}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredLoads.map((load, index) => (
              <div
                key={load._id}
                onClick={() => handleLoadClick(load._id)}
                className="bg-white rounded-lg shadow-sm border border-border-color hover:shadow-md transition-all duration-200 cursor-pointer group"
              >
                {/* Card Header */}
                <div className="p-6 border-b border-border-color">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-text-primary group-hover:text-primary-blue transition-colors">
                        Load #{index + 1}
                      </h3>
                      <p className="text-sm text-text-secondary">
                        {load.referenceId}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold text-accent-green">{formatCurrency(load.rate)}</div>
                      <div className="text-xs text-text-secondary">${load.perMileRate}/mile</div>
                    </div>
                  </div>

                  {/* Route */}
                  <div className="flex items-center gap-2 text-sm text-text-secondary mb-3">
                    <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span className="font-medium">{load.pickupLocation}</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                    <span className="font-medium">{load.deliveryLocation}</span>
                  </div>

                  {/* Priority Score */}
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-secondary-orange font-medium">
                      Priority: {load.ai.priorityScore}/100
                    </div>
                    {load.ai.call.callTriggered && (
                      <span className="bg-accent-green bg-opacity-10 text-accent-green px-2 py-1 rounded-full text-xs font-medium">
                        AI Called
                      </span>
                    )}
                  </div>
                </div>

                {/* Card Body */}
                <div className="p-6">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Distance</div>
                      <div className="text-sm font-medium text-text-primary">{load.distanceInMiles.toLocaleString()} mi</div>
                    </div>

                    <div>
                      <div className="text-xs text-text-secondary mb-1">Weight</div>
                      <div className="text-sm font-medium text-text-primary">{(load.weightInPounds / 1000).toFixed(1)}k lbs</div>
                    </div>

                    <div>
                      <div className="text-xs text-text-secondary mb-1">Equipment</div>
                      <div className="text-sm font-medium text-text-primary">{load.equipment}</div>
                    </div>

                    <div>
                      <div className="text-xs text-text-secondary mb-1">Load Size</div>
                      <div className="text-sm font-medium text-text-primary">{load.loadSize}</div>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div>
                      <div className="text-xs text-text-secondary">Broker</div>
                      <div className="text-sm font-medium text-text-primary">{load.broker.name}</div>
                    </div>

                    <div>
                      <div className="text-xs text-text-secondary">Pickup Date</div>
                      <div className="text-sm font-medium text-text-primary">{formatDate(load.pickupDate)}</div>
                    </div>
                  </div>

                  <div className="text-xs text-text-secondary">
                    Click to view full details
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadGroupDetails;
                <div>
                  <div>
                    <div className="text-xs text-text-secondary mb-1">Equipment</div>
                    <div className="text-sm font-medium text-text-primary">{load.equipment}</div>
                  </div>
                  
                  <div>
                    <div className="text-xs text-text-secondary mb-1">Load Size</div>
                    <div className="text-sm font-medium text-text-primary">{load.loadSize}</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="text-xs text-text-secondary mb-1">Pickup Date</div>
                    <div className="text-sm font-medium text-text-primary">{formatDate(load.pickupDate)}</div>
                  </div>
                  
                  <div>
                    <div className="text-xs text-text-secondary mb-1">Delivery Date</div>
                    <div className="text-sm font-medium text-text-primary">{formatDate(load.deliveryDate)}</div>
                  </div>
                </div>

                {/* Broker Information */}
                <div className="bg-background-light rounded-lg p-4 mb-4">
                  <h4 className="text-sm font-semibold text-text-primary mb-3">Broker Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Company</div>
                      <div className="text-sm font-medium text-text-primary">{load.broker.name}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Phone</div>
                      <div className="text-sm font-medium text-text-primary">{load.broker.phone}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Email</div>
                      <div className="text-sm font-medium text-text-primary">{load.broker.email}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-text-secondary mb-1">MC Number</div>
                      <div className="text-sm font-medium text-text-primary">{load.broker.mcNumber}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-text-secondary mb-1">DOT Number</div>
                      <div className="text-sm font-medium text-text-primary">{load.broker.usdotNumber}</div>
                    </div>
                    
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Credit Rating</div>
                      <div className="text-sm font-medium text-text-primary">{load.broker.creditRating}</div>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <div className="text-xs text-text-secondary mb-1">Address</div>
                    <div className="text-sm font-medium text-text-primary">{load.broker.address}</div>
                  </div>
                </div>

                {/* AI Call Information */}
                {load.ai.call.callTriggered && (
                  <div className="bg-primary-blue bg-opacity-5 rounded-lg p-4 mb-4">
                    <h4 className="text-sm font-semibold text-text-primary mb-3">AI Call Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <div className="text-xs text-text-secondary mb-1">Call Platform</div>
                        <div className="text-sm font-medium text-text-primary">{load.ai.call.callPlatform}</div>
                      </div>
                      
                      <div>
                        <div className="text-xs text-text-secondary mb-1">Call ID</div>
                        <div className="text-sm font-medium text-text-primary">{load.ai.call.callId}</div>
                      </div>
                      
                      <div>
                        <div className="text-xs text-text-secondary mb-1">Status</div>
                        <div className="text-sm font-medium text-accent-green">Call Triggered</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Delivery Comments */}
                {load.deliveryComments && (
                  <div>
                    <div className="text-xs text-text-secondary mb-1">Delivery Comments</div>
                    <div className="text-sm text-text-primary bg-background-light rounded-lg p-3">
                      {load.deliveryComments}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadGroupDetails;
