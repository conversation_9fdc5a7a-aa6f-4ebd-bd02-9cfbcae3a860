import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService, CallLog, Load } from '../services/api';
import Breadcrumb from '../components/Breadcrumbs/Breadcrumb';

interface CallLogDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  callLog: CallLog | null;
  load?: Load | null;
}

const CallLogDetailModal: React.FC<CallLogDetailModalProps> = ({ 
  isOpen, 
  onClose, 
  callLog, 
  load 
}) => {
  if (!isOpen || !callLog) return null;

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-border-color">
          <h3 className="text-xl font-bold text-neutral-gray-dark">
            Call Log Details
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-gray hover:text-neutral-gray-dark"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="p-6 space-y-6">
            {/* Call Overview */}
            <div className="bg-background-light rounded-lg p-4">
              <h4 className="font-semibold text-neutral-gray-dark mb-3">Call Overview</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-text-secondary">Type:</span>
                  <p className="font-medium capitalize">{callLog.type.replace(/([A-Z])/g, ' $1').trim()}</p>
                </div>
                <div>
                  <span className="text-text-secondary">Status:</span>
                  <p className="font-medium capitalize">{callLog.status}</p>
                </div>
                <div>
                  <span className="text-text-secondary">Started:</span>
                  <p className="font-medium">
                    {new Date(callLog.startedAt).toLocaleString()}
                  </p>
                </div>
                <div>
                  <span className="text-text-secondary">Duration:</span>
                  <p className="font-medium">
                    {callLog.endedAt 
                      ? formatDuration(Math.floor((new Date(callLog.endedAt).getTime() - new Date(callLog.startedAt).getTime()) / 1000))
                      : 'Ongoing'
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Load Information */}
            {load && (
              <div className="bg-background-light rounded-lg p-4">
                <h4 className="font-semibold text-neutral-gray-dark mb-3">Associated Load</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-text-secondary">Route:</span>
                    <p className="font-medium">{load.origin} → {load.destination}</p>
                  </div>
                  <div>
                    <span className="text-text-secondary">Rate:</span>
                    <p className="font-medium text-accent-green">${load.rate.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="text-text-secondary">Broker:</span>
                    <p className="font-medium">{load.broker.name}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Cost Breakdown */}
            {callLog.costBreakdown && (
              <div className="bg-background-light rounded-lg p-4">
                <h4 className="font-semibold text-neutral-gray-dark mb-3">Cost Breakdown</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-text-secondary">Speech-to-Text:</span>
                    <p className="font-medium">{formatCurrency(callLog.costBreakdown.stt)}</p>
                  </div>
                  <div>
                    <span className="text-text-secondary">LLM Processing:</span>
                    <p className="font-medium">{formatCurrency(callLog.costBreakdown.llm)}</p>
                  </div>
                  <div>
                    <span className="text-text-secondary">Text-to-Speech:</span>
                    <p className="font-medium">{formatCurrency(callLog.costBreakdown.tts)}</p>
                  </div>
                  <div>
                    <span className="text-text-secondary">VAPI:</span>
                    <p className="font-medium">{formatCurrency(callLog.costBreakdown.vapi)}</p>
                  </div>
                  <div>
                    <span className="text-text-secondary">Chat:</span>
                    <p className="font-medium">{formatCurrency(callLog.costBreakdown.chat)}</p>
                  </div>
                  <div>
                    <span className="text-text-secondary font-semibold">Total:</span>
                    <p className="font-bold text-secondary-orange">{formatCurrency(callLog.costBreakdown.total)}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Summary */}
            {callLog.summary && (
              <div className="bg-background-light rounded-lg p-4">
                <h4 className="font-semibold text-neutral-gray-dark mb-3">Call Summary</h4>
                <p className="text-text-primary leading-relaxed">{callLog.summary}</p>
              </div>
            )}

            {/* Conversation Messages */}
            <div className="bg-background-light rounded-lg p-4">
              <h4 className="font-semibold text-neutral-gray-dark mb-3">Conversation</h4>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {callLog.messages.map((message, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg ${
                      message.role === 'user' 
                        ? 'bg-primary-blue bg-opacity-10 ml-8' 
                        : message.role === 'bot'
                        ? 'bg-accent-green bg-opacity-10 mr-8'
                        : 'bg-neutral-gray bg-opacity-10'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className={`text-xs font-medium ${
                        message.role === 'user' ? 'text-primary-blue' :
                        message.role === 'bot' ? 'text-accent-green' :
                        'text-neutral-gray'
                      }`}>
                        {message.role === 'user' ? 'Caller' : 
                         message.role === 'bot' ? 'AI Assistant' : 
                         message.role.replace('_', ' ').toUpperCase()}
                      </span>
                      <span className="text-xs text-text-secondary">
                        {formatDuration(message.secondsFromStart)}
                        {message.duration && ` (${formatDuration(message.duration)})`}
                      </span>
                    </div>
                    <p className="text-sm text-text-primary">{message.message}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Recording */}
            {callLog.recordingUrl && (
              <div className="bg-background-light rounded-lg p-4">
                <h4 className="font-semibold text-neutral-gray-dark mb-3">Call Recording</h4>
                <audio controls className="w-full">
                  <source src={callLog.recordingUrl} type="audio/mpeg" />
                  Your browser does not support the audio element.
                </audio>
              </div>
            )}
          </div>
        </div>

        <div className="p-6 border-t border-border-color">
          <button
            onClick={onClose}
            className="truck-btn-primary w-full"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

const CallLogs: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const [callLogs, setCallLogs] = useState<CallLog[]>([]);
  const [loads, setLoads] = useState<Load[]>([]);
  const [loading, setLoading] = useState(true);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedCallLog, setSelectedCallLog] = useState<CallLog | null>(null);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  useEffect(() => {
    fetchCallLogs();
    fetchLoads();
  }, []);

  const fetchCallLogs = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (searchTerm) params.search = searchTerm;
      if (typeFilter) params.type = typeFilter;
      if (statusFilter) params.status = statusFilter;
      if (dateRange.start) params.startDate = dateRange.start;
      if (dateRange.end) params.endDate = dateRange.end;
      
      const response = await apiService.getCallLogs(params);
      setCallLogs(response.data.callLogs || []);
    } catch (error) {
      console.error('Error fetching call logs:', error);
      setError('Failed to load call logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchLoads = async () => {
    try {
      const response = await apiService.getLoads();
      setLoads(response.data.loads || []);
    } catch (error) {
      console.error('Error fetching loads:', error);
    }
  };

  const handleViewDetails = (callLog: CallLog) => {
    setSelectedCallLog(callLog);
    setDetailModalOpen(true);
  };

  const handleDeleteCallLog = async (callLogId: string) => {
    if (!window.confirm('Are you sure you want to delete this call log?')) return;
    
    try {
      await apiService.deleteCallLog(callLogId);
      fetchCallLogs();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to delete call log');
    }
  };

  const handleExportCallLogs = async (format: 'csv' | 'json') => {
    try {
      const response = await apiService.exportCallLogs({
        startDate: dateRange.start,
        endDate: dateRange.end,
        format
      });
      
      // Create download link
      const blob = new Blob([response.data], { 
        type: format === 'csv' ? 'text/csv' : 'application/json' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `call-logs-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to export call logs');
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-3 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'started':
        return `${baseClasses} bg-secondary-orange bg-opacity-10 text-secondary-orange`;
      case 'ended':
        return `${baseClasses} bg-accent-green bg-opacity-10 text-accent-green`;
      case 'failed':
        return `${baseClasses} bg-red-500 bg-opacity-10 text-red-600`;
      default:
        return `${baseClasses} bg-neutral-gray bg-opacity-10 text-neutral-gray`;
    }
  };

  const getCallTypeIcon = (type: string) => {
    if (type === 'inboundPhoneCall') {
      return (
        <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-4 h-4 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 3l4 4-4 4m-4-4h8M8 21l-4-4 4-4m4 4H4" />
        </svg>
      );
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getAssociatedLoad = (loadId?: string) => {
    if (!loadId) return null;
    return loads.find(load => load._id === loadId) || null;
  };

  const filteredCallLogs = callLogs.filter(callLog => {
    const matchesSearch = !searchTerm || 
      callLog.summary?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      callLog.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = !typeFilter || callLog.type === typeFilter;
    const matchesStatus = !statusFilter || callLog.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-blue rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-neutral-gray-dark mb-2">Loading Call Logs...</h2>
          <p className="text-neutral-gray">Please wait while we fetch call information</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Breadcrumb pageName="Call Logs" />
      
      <div className="professional-table">
        {/* Header */}
        <div className="p-6 border-b border-border-color">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-text-primary mb-1">Call Logs & AI Integration</h2>
              <p className="text-text-secondary">Track AI call conversations, costs, and performance metrics</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-text-secondary">
                Total: <span className="font-semibold text-text-primary">{filteredCallLogs.length}</span> calls
              </div>
              {isAdmin() && (
                <div className="flex gap-2">
                  <button
                    onClick={() => handleExportCallLogs('csv')}
                    className="px-4 py-2 border border-border-color rounded-lg text-text-secondary hover:bg-background-light transition-colors"
                  >
                    Export CSV
                  </button>
                  <button
                    onClick={() => handleExportCallLogs('json')}
                    className="px-4 py-2 border border-border-color rounded-lg text-text-secondary hover:bg-background-light transition-colors"
                  >
                    Export JSON
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search by call ID or summary..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              />
            </div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="">All Types</option>
              <option value="inboundPhoneCall">Inbound</option>
              <option value="outboundPhoneCall">Outbound</option>
            </select>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
            >
              <option value="">All Status</option>
              <option value="started">Started</option>
              <option value="ended">Ended</option>
              <option value="failed">Failed</option>
            </select>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              placeholder="Start Date"
            />
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="px-4 py-2 border border-border-color rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-colors"
              placeholder="End Date"
            />
          </div>
        </div>

        {error && (
          <div className="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          {/* Table Header */}
          <div className="professional-table-header">
            <div className="grid grid-cols-12 gap-4 px-6 py-4 text-sm font-semibold text-text-primary">
              <div className="col-span-2">Date & Time</div>
              <div className="col-span-2">Type & Status</div>
              <div className="col-span-3">Load Information</div>
              <div className="col-span-2">Duration & Cost</div>
              <div className="col-span-2">Summary</div>
              <div className="col-span-1">Actions</div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border-color">
            {filteredCallLogs.length > 0 ? (
              filteredCallLogs.map((callLog) => {
                const associatedLoad = getAssociatedLoad(callLog.loadId);
                const duration = callLog.endedAt
                  ? Math.floor((new Date(callLog.endedAt).getTime() - new Date(callLog.startedAt).getTime()) / 1000)
                  : 0;

                return (
                  <div
                    key={callLog.id}
                    className="professional-table-row grid grid-cols-12 gap-4 px-6 py-4 items-center"
                  >
                    {/* Date & Time */}
                    <div className="col-span-2">
                      <div>
                        <p className="font-medium text-text-primary">
                          {new Date(callLog.startedAt).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-text-secondary">
                          {new Date(callLog.startedAt).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>

                    {/* Type & Status */}
                    <div className="col-span-2">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          {getCallTypeIcon(callLog.type)}
                          <span className="text-sm font-medium">
                            {callLog.type === 'inboundPhoneCall' ? 'Inbound' : 'Outbound'}
                          </span>
                        </div>
                        <div>
                          {getStatusBadge(callLog.status)}
                        </div>
                      </div>
                    </div>

                    {/* Load Information */}
                    <div className="col-span-3">
                      {associatedLoad ? (
                        <div>
                          <p className="font-medium text-text-primary text-sm">
                            {associatedLoad.origin} → {associatedLoad.destination}
                          </p>
                          <p className="text-xs text-text-secondary">
                            {associatedLoad.broker.name} | ${associatedLoad.rate.toLocaleString()}
                          </p>
                        </div>
                      ) : (
                        <p className="text-sm text-text-secondary">No associated load</p>
                      )}
                    </div>

                    {/* Duration & Cost */}
                    <div className="col-span-2">
                      <div>
                        <p className="font-medium text-text-primary">
                          {duration > 0 ? formatDuration(duration) : 'Ongoing'}
                        </p>
                        {callLog.costBreakdown && (
                          <p className="text-sm text-secondary-orange font-medium">
                            ${callLog.costBreakdown.total.toFixed(4)}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Summary */}
                    <div className="col-span-2">
                      <p className="text-sm text-text-primary line-clamp-2">
                        {callLog.summary || 'No summary available'}
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="col-span-1">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleViewDetails(callLog)}
                          className="p-2 text-primary-blue hover:bg-primary-blue hover:bg-opacity-10 rounded-lg transition-colors"
                          title="View details"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>

                        {isAdmin() && (
                          <button
                            onClick={() => handleDeleteCallLog(callLog.id)}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                            title="Delete call log"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="px-6 py-12 text-center">
                <div className="flex flex-col items-center gap-3">
                  <svg className="w-12 h-12 text-neutral-gray-light" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary mb-1">No call logs found</h3>
                    <p className="text-text-secondary">Call logs will appear here after AI calls are made</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <CallLogDetailModal
        isOpen={detailModalOpen}
        onClose={() => {
          setDetailModalOpen(false);
          setSelectedCallLog(null);
        }}
        callLog={selectedCallLog}
        load={selectedCallLog ? getAssociatedLoad(selectedCallLog.loadId) : null}
      />
    </>
  );
};

export default CallLogs;
