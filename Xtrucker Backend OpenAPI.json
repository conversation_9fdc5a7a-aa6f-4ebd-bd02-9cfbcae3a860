{"openapi": "3.0.0", "info": {"title": "XTrucker Backend API", "version": "1.0.0", "description": "API documentation for XTrucker AI application"}, "servers": [{"url": "http://localhost:8080"}], "paths": {"/api/dispatcher/company": {"get": {"summary": "Get company information", "tags": ["Dispatcher Company"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Company information retrieved successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Company not found"}}}, "put": {"summary": "Update company information", "tags": ["Dispatcher Company"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}}}}}}, "responses": {"200": {"description": "Company information updated successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}}}}, "/api/dispatcher/users": {"get": {"summary": "Get all team members", "tags": ["Team Management"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Team members retrieved successfully"}, "401": {"description": "Unauthorized"}}}, "post": {"summary": "Invite a new team member", "tags": ["Team Management"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "role": {"type": "string", "enum": ["admin", "agent"], "default": "agent"}}}}}}, "responses": {"201": {"description": "Team member invited successfully"}, "400": {"description": "Invalid input or user already exists"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required or user limit reached"}}}}, "/api/dispatcher/users/{userId}": {"put": {"summary": "Update team member", "tags": ["Team Management"], "security": [{"bearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "The ID of the team member", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "role": {"type": "string", "enum": ["admin", "agent"]}, "status": {"type": "string", "enum": ["active", "inactive"]}}}}}}, "responses": {"200": {"description": "Team member updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}, "404": {"description": "Team member not found"}}}, "delete": {"summary": "Remove team member", "tags": ["Team Management"], "security": [{"bearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "description": "The ID of the team member", "schema": {"type": "string"}}], "responses": {"200": {"description": "Team member removed successfully"}, "400": {"description": "Cannot remove yourself or last admin"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}, "404": {"description": "Team member not found"}}}}, "/api/drivers": {"get": {"summary": "Get all drivers", "tags": ["Drivers"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "description": "Number of drivers per page", "schema": {"type": "integer", "default": 10}}, {"name": "search", "in": "query", "description": "Search by name, phone, or license number", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["active", "inactive", "suspended"]}}, {"name": "truckingCompanyId", "in": "query", "description": "Filter by trucking company", "schema": {"type": "string"}}, {"name": "available", "in": "query", "description": "Filter by availability", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "List of drivers"}, "401": {"description": "Unauthorized"}}}, "post": {"summary": "Create new driver", "tags": ["Drivers"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "phone", "licenseNumber", "truckingCompanyId"], "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "licenseNumber": {"type": "string"}, "licenseState": {"type": "string"}, "licenseExpiryDate": {"type": "string", "format": "date"}, "truckingCompanyId": {"type": "string"}, "truckType": {"type": "string", "enum": ["dry_van", "refrigerated", "flatbed", "tanker", "car_carrier", "heavy_haul"]}, "truckMake": {"type": "string"}, "truckModel": {"type": "string"}, "truckYear": {"type": "integer"}, "trailerType": {"type": "string"}, "maxWeight": {"type": "number"}, "currentLocation": {"type": "string"}, "homeBase": {"type": "string"}, "notes": {"type": "string"}}}}}}, "responses": {"201": {"description": "Driver created successfully"}, "400": {"description": "Invalid input or driver already exists"}, "401": {"description": "Unauthorized"}}}}, "/api/drivers/{id}": {"get": {"summary": "Get driver by ID", "tags": ["Drivers"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Driver ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Driver details"}, "401": {"description": "Unauthorized"}, "404": {"description": "Driver not found"}}}, "put": {"summary": "Update driver", "tags": ["Drivers"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Driver ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "licenseNumber": {"type": "string"}, "licenseState": {"type": "string"}, "licenseExpiryDate": {"type": "string", "format": "date"}, "truckType": {"type": "string", "enum": ["dry_van", "refrigerated", "flatbed", "tanker", "car_carrier", "heavy_haul"]}, "truckMake": {"type": "string"}, "truckModel": {"type": "string"}, "truckYear": {"type": "integer"}, "trailerType": {"type": "string"}, "maxWeight": {"type": "number"}, "currentLocation": {"type": "string"}, "homeBase": {"type": "string"}, "available": {"type": "boolean"}, "status": {"type": "string", "enum": ["active", "inactive", "suspended"]}, "notes": {"type": "string"}}}}}}, "responses": {"200": {"description": "Driver updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "404": {"description": "Driver not found"}}}, "delete": {"summary": "Delete driver", "tags": ["Drivers"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Driver ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Driver deleted successfully"}, "400": {"description": "Cannot delete driver with active loads"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}, "404": {"description": "Driver not found"}}}}, "/api/drivers/{id}/availability": {"patch": {"summary": "Update driver availability and location", "tags": ["Drivers"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Driver ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"available": {"type": "boolean"}, "currentLocation": {"type": "string"}}}}}}, "responses": {"200": {"description": "Driver availability updated successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Driver not found"}}}}, "/scrape": {"post": {"summary": "Get all loads", "responses": {"200": {"description": "Successfully retrieved all loads"}}}}, "/current": {"post": {"summary": "Get the current load", "responses": {"200": {"description": "Successfully retrieved the current load"}}}}, "/call-logs": {"post": {"summary": "Save a call log", "responses": {"201": {"description": "Call log saved successfully"}}}}, "/call-logs/{id}": {"get": {"summary": "Get a call log by ID", "parameters": [{"name": "id", "in": "path", "required": true, "description": "The ID of the call log", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved call log"}, "404": {"description": "Call log not found"}}}}, "/call-logs/user/{userId}": {"get": {"summary": "Get call logs by user ID", "parameters": [{"name": "userId", "in": "path", "required": true, "description": "The ID of the user", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved call logs for user"}}}}, "/{loadId}": {"get": {"summary": "Get load by load ID", "parameters": [{"name": "loadId", "in": "path", "required": true, "description": "The ID of the load", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved load"}, "404": {"description": "Load not found"}}}}, "/user/{userId}": {"get": {"summary": "Get loads by user ID", "parameters": [{"name": "userId", "in": "path", "required": true, "description": "The ID of the user", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved loads for user"}}}}, "/current/user/{userId}": {"get": {"summary": "Get all current loads by user ID", "parameters": [{"name": "userId", "in": "path", "required": true, "description": "The ID of the user", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved current loads for user"}}}}, "/current/{loadId}": {"get": {"summary": "Get current load by load ID", "parameters": [{"name": "loadId", "in": "path", "required": true, "description": "The ID of the current load", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved the current load"}, "404": {"description": "Current load not found"}}}}, "/api/subscription/current": {"get": {"summary": "Get current subscription information", "tags": ["Subscription"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Current subscription information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "subscription": {"type": "object", "properties": {"currentPlan": {"type": "string", "enum": ["basic", "pro", "enterprise"]}, "planDetails": {"type": "object"}, "maxUsers": {"type": "number"}, "activeUsers": {"type": "number"}, "canAddUsers": {"type": "boolean"}}}}}}}}, "401": {"description": "Unauthorized"}}}}, "/api/subscription/plans": {"get": {"summary": "Get all available subscription plans", "tags": ["Subscription"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Available subscription plans", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "plans": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string"}, "name": {"type": "string"}, "maxUsers": {"type": "number"}, "maxDrivers": {"type": "number"}, "price": {"type": "number"}, "features": {"type": "array", "items": {"type": "string"}}, "isCurrent": {"type": "boolean"}, "isUpgrade": {"type": "boolean"}, "isDowngrade": {"type": "boolean"}}}}}}}}}, "401": {"description": "Unauthorized"}}}}, "/api/subscription/upgrade": {"post": {"summary": "Upgrade subscription plan", "tags": ["Subscription"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["newPlan"], "properties": {"newPlan": {"type": "string", "enum": ["basic", "pro", "enterprise"], "description": "The new subscription plan to upgrade to"}}}}}}, "responses": {"200": {"description": "Subscription upgraded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "subscription": {"type": "object"}}}}}}, "400": {"description": "Invalid plan or not an upgrade"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}}}}, "/api/subscription/downgrade": {"post": {"summary": "Downgrade subscription plan", "tags": ["Subscription"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["newPlan"], "properties": {"newPlan": {"type": "string", "enum": ["basic", "pro", "enterprise"], "description": "The new subscription plan to downgrade to"}}}}}}, "responses": {"200": {"description": "Subscription downgraded successfully"}, "400": {"description": "Invalid plan, not a downgrade, or too many active users"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}}}}, "/api/subscription/usage": {"get": {"summary": "Get subscription usage statistics", "tags": ["Subscription"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Subscription usage statistics", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "usage": {"type": "object", "properties": {"users": {"type": "object", "properties": {"current": {"type": "number"}, "limit": {"type": "number"}, "percentage": {"type": "number"}}}, "plan": {"type": "object"}, "billingPeriod": {"type": "object"}}}}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}}}}, "/api/trucking-companies": {"get": {"summary": "Get all trucking companies", "tags": ["Trucking Companies"], "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "description": "Number of companies per page", "schema": {"type": "integer", "default": 10}}, {"name": "search", "in": "query", "description": "Search by name, MC number, or DOT number", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["active", "inactive", "suspended"]}}, {"name": "verified", "in": "query", "description": "Filter by verification status", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "List of trucking companies"}, "401": {"description": "Unauthorized"}}}, "post": {"summary": "Create new trucking company", "tags": ["Trucking Companies"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "mcNumber", "dotNumber"], "properties": {"name": {"type": "string"}, "mcNumber": {"type": "string"}, "dotNumber": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "contactPersonName": {"type": "string"}, "contactPersonPhone": {"type": "string"}, "contactPersonEmail": {"type": "string"}, "insuranceProvider": {"type": "string"}, "insuranceExpiryDate": {"type": "string", "format": "date"}, "creditRating": {"type": "string", "enum": ["excellent", "good", "fair", "poor", "not_rated"]}, "notes": {"type": "string"}}}}}}, "responses": {"201": {"description": "Trucking company created successfully"}, "400": {"description": "Invalid input or company already exists"}, "401": {"description": "Unauthorized"}}}}, "/api/trucking-companies/{id}": {"get": {"summary": "Get trucking company by ID", "tags": ["Trucking Companies"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Trucking company ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Trucking company details"}, "401": {"description": "Unauthorized"}, "404": {"description": "Trucking company not found"}}}, "put": {"summary": "Update trucking company", "tags": ["Trucking Companies"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Trucking company ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "mcNumber": {"type": "string"}, "dotNumber": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "contactPersonName": {"type": "string"}, "contactPersonPhone": {"type": "string"}, "contactPersonEmail": {"type": "string"}, "insuranceProvider": {"type": "string"}, "insuranceExpiryDate": {"type": "string", "format": "date"}, "creditRating": {"type": "string", "enum": ["excellent", "good", "fair", "poor", "not_rated"]}, "notes": {"type": "string"}, "status": {"type": "string", "enum": ["active", "inactive", "suspended"]}}}}}}, "responses": {"200": {"description": "Trucking company updated successfully"}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}, "404": {"description": "Trucking company not found"}}}, "delete": {"summary": "Delete trucking company", "tags": ["Trucking Companies"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Trucking company ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Trucking company deleted successfully"}, "400": {"description": "Cannot delete company with active drivers"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}, "404": {"description": "Trucking company not found"}}}}, "/api/trucking-companies/{id}/verify": {"post": {"summary": "Verify or unverify trucking company", "tags": ["Trucking Companies"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Trucking company ID", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["verified"], "properties": {"verified": {"type": "boolean"}, "verificationNotes": {"type": "string"}}}}}}, "responses": {"200": {"description": "Trucking company verification status updated"}, "401": {"description": "Unauthorized"}, "403": {"description": "Admin access required"}, "404": {"description": "Trucking company not found"}}}}}, "components": {}, "tags": []}