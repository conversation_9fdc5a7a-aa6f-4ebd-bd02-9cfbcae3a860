import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService, DispatcherUser, DispatcherCompany } from '../services/api';

interface AuthContextType {
  user: DispatcherUser | null;
  company: DispatcherCompany | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (companyName: string, email: string, password: string, name: string) => Promise<void>;
  logout: () => void;
  isAdmin: () => boolean;
  isAgent: () => boolean;
  canAddUsers: () => boolean;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<DispatcherUser | null>(null);
  const [company, setCompany] = useState<DispatcherCompany | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  const isAdmin = () => user?.role === 'admin';
  const isAgent = () => user?.role === 'agent';
  
  const canAddUsers = () => {
    if (!company || !isAdmin()) return false;
    return company.activeUsers < company.maxUsers;
  };

  const refreshUserData = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        setIsLoading(false);
        return;
      }

      // Get user data from token or API call
      // For now, we'll simulate this with stored user data
      const storedUser = localStorage.getItem('userData');
      const storedCompany = localStorage.getItem('companyData');
      
      if (storedUser && storedCompany) {
        setUser(JSON.parse(storedUser));
        setCompany(JSON.parse(storedCompany));
      } else {
        // In production, fetch from API
        // const userResponse = await apiService.getCurrentUser();
        // const companyResponse = await apiService.getCompany();
        // setUser(userResponse.data);
        // setCompany(companyResponse.data);
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.login(email, password);
      
      // Extract token and user data from response
      const { token, user: userData, company: companyData } = response.data;
      
      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(userData));
      localStorage.setItem('companyData', JSON.stringify(companyData));
      
      setUser(userData);
      setCompany(companyData);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (companyName: string, email: string, password: string, name: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.signup(companyName, email, password, name);
      
      // Extract token and user data from response
      const { token, user: userData, company: companyData } = response.data;
      
      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(userData));
      localStorage.setItem('companyData', JSON.stringify(companyData));
      
      setUser(userData);
      setCompany(companyData);
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    localStorage.removeItem('companyData');
    setUser(null);
    setCompany(null);
  };

  useEffect(() => {
    refreshUserData();
  }, []);

  const value: AuthContextType = {
    user,
    company,
    isAuthenticated,
    isLoading,
    login,
    signup,
    logout,
    isAdmin,
    isAgent,
    canAddUsers,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
